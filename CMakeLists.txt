cmake_minimum_required(VERSION 3.23)
project(workspace)

set(CMAKE_VERBOSE_MAKEFILE ON)
set(CMAKE_BUILD_TYPE Debug)

set_property(GLOBAL PROPERTY WORKSPACE_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set_property(GLOBAL PROPERTY WORKSPACE_BUILD_DIR ${CMAKE_CURRENT_BINARY_DIR})

set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_CURRENT_SOURCE_DIR}/cmake)

include(public/GlobalConfig)
set_compiler_options()

include(internal/Module)

add_subdirectory(framework)