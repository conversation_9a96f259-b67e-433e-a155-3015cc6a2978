project(msg_queue)

add_library(${PROJECT_NAME} 
    SHARED 
        src/message_queue_impl.cpp
)
target_include_module_directories(${PROJECT_NAME}
    PUBLIC
        log
        utils
)
target_include_directories(${PROJECT_NAME} 
    PUBLIC 
        include
        private
)
target_link_libraries(${PROJECT_NAME} 
    PUBLIC 
        pthread 
        rt
        log
)


if (BUILD_EXAMPLE) 
    add_executable(${PROJECT_NAME}_example
        example/mq_example.cpp
    )
    target_link_directories(${PROJECT_NAME}_example
        PUBLIC
        "/usr/local/lib/x86_64-linux-gnu/")
 
    target_link_libraries(${PROJECT_NAME}_example
        PUBLIC
            ${PROJECT_NAME}
            fmt
    )
    install(TARGETS ${PROJECT_NAME}_example DESTINATION bin)
endif()
