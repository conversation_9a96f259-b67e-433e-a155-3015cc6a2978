#include "message_queue_impl.hpp"

#include "log/log.hpp"
#include "mq/mq_const.hpp"

namespace ws::mq {

MessageQueueImpl::MessageQueueImpl(std::string const& mq_name, int64_t const msg_size, OpMode const mode,
                                   int32_t const queue_size, bool const unlink_on_quit)
    : MessageQueue{},
      msg_size_{msg_size},
      queue_desc_{Open(mq_name, mode, queue_size)},
      mq_name_{mq_name},
      unlink_on_quit_{unlink_on_quit} {}

MessageQueueImpl::~MessageQueueImpl() noexcept {
    int32_t const status{MessageQueueImpl::Close()};
    if (status != kStatusOk) {
        log::Error(kLogTag, "Failed to close message queue: %d: %s", status, mq_name_.c_str());
    }
}

mqd_t MessageQueueImpl::Open(std::string const& mq_name, OpMode const mode, int64_t const queue_size) {
    if (mq_name.empty()) {
        log::Error(kLogTag, "Invalid message queue name: %s", mq_name.c_str());
        return kStatusInvalid;
    }

    constexpr int32_t kFlagRw{O_RDWR | O_CREAT};
    constexpr int32_t kFlagRo{O_RDONLY | O_CREAT};

    constexpr int32_t kAccessRw{S_IRUSR | S_IWUSR | S_IRGRP | S_IWGRP};
    constexpr int32_t kAccessRo{S_IRUSR | S_IRGRP};

    bool const read_only{OpMode::kReadOnly == mode};
    int32_t const open_flag{read_only ? kFlagRo : kFlagRw};
    int32_t const access_mode{read_only ? kAccessRo : kAccessRw};

    mq_attr attr{0L, queue_size, msg_size_, 0L, {0L}};
    mqd_t const local_desc{mq_open(mq_name.c_str(), open_flag, access_mode, &attr)};
    if (local_desc == kStatusInvalid) {
        log::Error(kLogTag, "Failed to open message queue: %s, error: %s", mq_name.c_str(), strerror(errno));
        return kStatusInvalid;
    }
    return local_desc;
}

int32_t MessageQueueImpl::Close() const noexcept {
    if (queue_desc_ == kStatusInvalid) {
        log::Error(kLogTag, "Invalid message queue: %d: %s", queue_desc_, mq_name_.c_str());
        return queue_desc_;
    }
    int32_t status{mq_close(queue_desc_)};
    if (status != kStatusOk) {
        log::Error(kLogTag, "Failed to close message queue: %d: %s", status, mq_name_.c_str());
    }

    if (unlink_on_quit_) {
        status = mq_unlink(mq_name_.c_str());
        if (status != kStatusOk) {
            if (errno != ENOENT) {
                log::Warn(kLogTag, "try to unlink a non-existed message queue%s", mq_name_.c_str());
                status = kStatusOk;
            } else {
                log::Error(kLogTag, "Failed to unlink message queue: %s %d: %s", mq_name_.c_str(), status,
                           strerror(errno));
            }
        }
    }

    return status;
}

int32_t MessageQueueImpl::Send(char const* const msg, uint32_t const priority) const noexcept {
    if (queue_desc_ == kStatusInvalid) {
        log::Error(kLogTag, "Invalid message queue: %d: %s", queue_desc_, mq_name_.c_str());
        return queue_desc_;
    }

    if (msg_size_ < 1) {
        log::Error(kLogTag, "Invalid message size: %ld", msg_size_);
        return kStatusInvalid;
    }

    if (msg == nullptr) {
        log::Error(kLogTag, "Invalid message pointer");
        return kStatusInvalid;
    }

    struct timespec ts = AfterNowTimeSpec(kMqTimeout);
    // log::Info(kLogTag, "time: %d, %d", ts.tv_sec, ts.tv_nsec);
    // log::Debug(kLogTag, "queue_desc: %d, msg_size: %ld, msg: %s", queue_desc_, msg_size_, msg);

    int const ret = mq_timedsend(queue_desc_, msg, static_cast<uint64_t>(msg_size_), priority, &ts);
    if (ret < kStatusOk) {
        log::Error(kLogTag, "Failed to send message: %s", strerror(errno));
    }
    return ret;
}

int32_t MessageQueueImpl::Receive(char* msg, uint32_t& priority) const noexcept {
    if (queue_desc_ == kStatusInvalid) {
        log::Error(kLogTag, "Invalid message queue: %d: %s", queue_desc_, mq_name_.c_str());
        return queue_desc_;
    }
    if (msg_size_ < 1) {
        log::Error(kLogTag, "Invalid message size: %ld", msg_size_);
        return kStatusInvalid;
    }
    if (msg == nullptr) {
        log::Error(kLogTag, "Invalid message pointer");
        return kStatusInvalid;
    }

    // 返回值：成功返回接收到的消息字节数；失败返回-1
    int64_t const status{mq_receive(queue_desc_, msg, static_cast<uint64_t>(msg_size_), &priority)};
    if (status < kStatusOk) {
        log::Error(kLogTag, "Failed to receive message: %s", strerror(errno));
    }
    return kStatusOk;
}

bool MessageQueueImpl::Status() const noexcept { return queue_desc_ > 0; }

int64_t MessageQueueImpl::CurrentCount() const noexcept {
    mq_attr attr{0L, 0L, 0L, 0L, {0L}};
    int32_t const status{mq_getattr(queue_desc_, &attr)};

    if (queue_desc_ < kStatusOk) {
        log::Error(kLogTag, "Invalid message queue: %d: %s", queue_desc_, mq_name_.c_str());
        return status;
    }

    return attr.mq_curmsgs;
}

std::unique_ptr<MessageQueue> MessageQueue::Create(std::string const& mq_name, int64_t const msg_size,
                                                   OpMode const mode, int32_t const queue_size,
                                                   bool const unlink_on_quit) noexcept {
    return std::make_unique<MessageQueueImpl>(mq_name, msg_size, mode, queue_size, unlink_on_quit);
}

struct timespec MessageQueueImpl::NowTimeSpec() const noexcept {
    struct timespec now{0L, 0L};
    clock_gettime(CLOCK_REALTIME, &now);
    return now;
}

struct timespec MessageQueueImpl::AfterNowTimeSpec(int64_t const ns) const noexcept {
    struct timespec ts{NowTimeSpec()};

    ts.tv_nsec += ns;
    static constexpr int64_t kNanoSecPerSec{1'000'000'000L};
    int64_t const sec_num{ts.tv_nsec / kNanoSecPerSec};
    if (sec_num > 0) {
        ts.tv_sec += sec_num;
        ts.tv_nsec -= sec_num * kNanoSecPerSec;
    }
    return ts;
}

}  // namespace ws::mq
