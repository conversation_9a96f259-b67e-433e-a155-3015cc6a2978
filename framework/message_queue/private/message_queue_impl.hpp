#ifndef MESSAGE_QUEUE_IMPL_HPP_
#define MESSAGE_QUEUE_IMPL_HPP_

#include <mqueue.h>  // system message queue

#include <string>

#include "mq/msg_queue.hpp"
namespace ws::mq {

class MessageQueueImpl : public MessageQueue {
public:
    MessageQueueImpl(std::string const& mq_name, int64_t const msg_size, OpMode const mode,
                     int32_t const queue_size = kQueueSize, bool const unlink_on_quit = false);
    ~MessageQueueImpl() noexcept override;
    DISABLE_COPY_AND_MOVE(MessageQueueImpl);

    [[nodiscard]]
    int32_t Close() const noexcept override;
    int32_t Send(char const* const msg, uint32_t const priority = 0U) const noexcept override;
    int32_t Receive(char* msg, uint32_t& priority) const noexcept override;
    [[nodiscard]]
    bool Status() const noexcept override;
    [[nodiscard]]
    int64_t CurrentCount() const noexcept override;

private:
    mqd_t Open(std::string const& mq_name, OpMode const mode, int64_t const queue_size);

    struct timespec NowTimeSpec() const noexcept;
    struct timespec AfterNowTimeSpec(int64_t const ns) const noexcept;

    int64_t msg_size_;
    mqd_t queue_desc_{-1};  // 消息队列描述符，类似于fd
    std::string mq_name_;
    bool unlink_on_quit_;

    static constexpr char const* kLogTag{"MessageQueueImpl"};
    static constexpr int64_t const kMqTimeout{1'000'000'000L};  // 超时时间设置为1s，此处单位为纳秒
};

}  // namespace ws::mq

#endif  // MESSAGE_QUEUE_IMPL_HPP_
