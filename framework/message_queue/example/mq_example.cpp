#include "log/log.hpp"
#include "mq/mq_ipc.hpp"

struct RequestIdl {
    uint32_t id;
    uint8_t reboot_mode;
};

struct ResponseIdl {
    uint32_t id;
    uint8_t reboot_mode;
};

int main() {
    auto const topic{"/mq_topic_channel"};
    ws::mq::MqIpcPublisher<RequestIdl> publisher{topic};

    ws::mq::MqIpcSubscriber<ResponseIdl> subscriber{topic};

    subscriber.Subscribe([](ResponseIdl const& response) {
        ws::log::InfoFmt("MqIpcSubscriber", "Received response: id={}, reboot_mode={}", response.id,
                         response.reboot_mode);
        return 0;
    });

    for (int i = 0; i < 10; ++i) {
        RequestIdl request{static_cast<uint32_t>(i), 0};
        int32_t const status = publisher.Publish(request);
        ws::log::InfoFmt("MqIpcPublisher", "Published request: id={}, reboot_mode={}, status={}", request.id,
                         request.reboot_mode, status);
    }

    std::this_thread::sleep_for(std::chrono::seconds(1));
    return 0;
}
