#ifndef MQ_CONST_HPP_
#define MQ_CONST_HPP_

#include <string>

namespace ws::mq {

static constexpr int32_t kStatusOk{0};
static constexpr int32_t kStatusInvalid{-1};
constexpr static char const* kRequestPostfix{"_req"};
constexpr static char const* kResponsePostfix{"_res"};
constexpr static char const* kTopicPostfix{"_topic"};
constexpr static char const* kReplierThreadPrefix{"3MR"};
constexpr static char const* kSubscriberThreadPrefix{"_3MS"};

inline std::string GetRequestChannelName(std::string const& mq_name) noexcept { return mq_name + kRequestPostfix; }

inline std::string GetResponseChannelName(std::string const& mq_name) noexcept { return mq_name + kResponsePostfix; }

inline std::string GetTopicChannelName(std::string const& mq_name) noexcept { return mq_name + kTopicPostfix; }

inline std::string GetReplierThreadName(std::string const& mq_name) noexcept { return kReplierThreadPrefix + mq_name; }

inline std::string GetSubscriberThreadName(std::string const& mq_name) noexcept {
    return kSubscriberThreadPrefix + mq_name;
}

}  // namespace ws::mq

#endif  // MQ_CONST_HPP_