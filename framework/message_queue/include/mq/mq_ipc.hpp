#ifndef MQ_IPC_HPP_
#define MQ_IPC_HPP_

#include <atomic>
#include <string>
#include <thread>

#include "mq/mq_channel.hpp"
#include "mq/mq_const.hpp"

namespace ws::mq {

/**
 * \brief  The IPC publisher class with POSIX message queue.
 *
 * \tparam Message Type of the POD message.
 */
template <typename Message>
class MqIpcPublisher {
public:
    explicit MqIpcPublisher(std::string const& mq_name)
        : topic_channel_{GetTopicChannelName(mq_name), false}, mq_name_{mq_name} {}

    [[nodiscard]]
    int32_t Publish(Message const& message) {
        int32_t const status = topic_channel_.SendData(message);
        if (status != 0) {
            log::Error(kLogTag, "Failed to publish message, %s [%d]", mq_name_.c_str(), status);
        }
        return status;
    }

private:
    MqChannel<Message> topic_channel_;
    std::string mq_name_;
    static constexpr char const* kLogTag{"MqIpcPublisher"};
};

/**
 * \brief  The IPC subscriber class with POSIX message queue.
 *
 * \tparam Message Type of the POD message.
 */
template <typename Message>
class MqIpcSubscriber {
public:
    explicit MqIpcSubscriber(std::string const& mq_name)
        : topic_channel_{GetTopicChannelName(mq_name), true}, to_quit_{true}, mq_name_{mq_name}, worker_{} {}

    virtual ~MqIpcSubscriber() { Unsubscribe(); }

    template <typename Callback>
    void Subscribe(Callback&& callback) noexcept {
        if (!topic_channel_.CheckMqStatus()) {
            log::Error(kLogTag, "Failed to check mq status, %s", mq_name_.c_str());
            return;
        }

        to_quit_.store(false);

        worker_ = std::thread([this, cb = std::forward<Callback>(callback)]() { WaitHandleMessage(cb); });
    }

    void Unsubscribe() noexcept {
        if (!to_quit_.load()) {
            to_quit_.store(true);

            // send empty message to unblock the waiting.
            Message const message{};
            topic_channel_.SendData(message);
        }
        if (worker_.joinable()) {
            worker_.join();
        }
    }

private:
    template <typename Callback>
    void WaitHandleMessage(Callback&& callback) noexcept {
        while (!to_quit_.load()) {
            Message message{};
            int32_t status = topic_channel_.RecvData(message);
            if (status != 0) {
                log::Error(kLogTag, "Failed to receive message, %s [%d]", mq_name_.c_str(), status);
                continue;
            }

            if (to_quit_.load()) {
                log::Error(kLogTag, "To quit, %s", mq_name_.c_str());
                break;
            }

            status = callback(message);
            if (status != 0) {
                log::Error(kLogTag, "Failed to handle message, %s [%d]", mq_name_.c_str(), status);
                continue;
            }
        }
    }

    MqChannel<Message> topic_channel_;
    std::atomic_bool to_quit_;
    std::string mq_name_;
    std::thread worker_;

    static constexpr char const* kLogTag{"MqIpcSubscriber"};
};

}  // namespace ws::mq

#endif  // MQ_IPC_HPP_
