#ifndef MQ_CHANNEL_HPP_
#define MQ_CHANNEL_HPP_

#include <memory>
#include <string>
#include <type_traits>

#include "log/log.hpp"
#include "mq/mq_channel.hpp"
#include "mq/mq_const.hpp"
#include "mq/msg_queue.hpp"

namespace ws::mq {

/**
 * \brief  The internal helper class for chanel by message queue. 类似于pimpl的类，没有啥实际作用。
 *
 * \tparam Message Type of the POD message.
 */
template <typename Message>
class MqChannel {
public:
    MqChannel(std::string const& mq_name, bool const is_owner)
        : mq_ptr_{Create(mq_name, kMsgSize, kQueueSize, is_owner)}, channel_name_{mq_name} {
        static_assert(std::is_trivial_v<Message>, "Message must be POD type.");
    }

    int32_t SendData(Message const& msg) const noexcept {
        int32_t const status{mq_ptr_->Send(reinterpret_cast<char const*>(&msg))};
        if (status != kStatusOk) {
            log::Error(kLogTag, "SendData failed, status: %d, channel_name: %s", status, channel_name_.c_str());
        }
        return status;
    }

    int32_t RecvData(Message& msg) const noexcept {
        uint32_t priority{0U};
        int32_t const status{mq_ptr_->Receive(reinterpret_cast<char*>(&msg), priority)};
        if (status != kStatusOk) {
            log::Error(kLogTag, "RecvData failed, status: %d, channel_name: %s", status, channel_name_.c_str());
        }

        return status;
    }

    [[nodiscard]] bool CheckMqStatus() const noexcept { return mq_ptr_->Status(); }

private:
    static std::unique_ptr<MessageQueue> Create(std::string const& mq_name, uint64_t msg_size, int32_t queue_size,
                                                bool const is_owner) {
        return MessageQueue::Create(mq_name, msg_size, MessageQueue::OpMode::kReadWrite, queue_size, is_owner);
    }

    std::unique_ptr<MessageQueue> mq_ptr_;
    std::string channel_name_;

    static constexpr int32_t kQueueSize{10};
    static constexpr uint64_t kMsgSize{sizeof(Message)};
    static constexpr char const* kLogTag{"MqChannel"};
};

}  // namespace ws::mq

#endif  // MQ_CHANNEL_HPP_