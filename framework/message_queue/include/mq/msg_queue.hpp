#ifndef MQ_MSG_QUEUE_HPP_
#define MQ_MSG_QUEUE_HPP_

#include <cerrno>
#include <cstring>
#include <memory>

#include "utils/macros.hpp"

/***POSIX 消息队列是 "message-oriented"（面向消息的），不像管道或共享内存是 "byte-stream"（面向字节流的）。

msg_len 确保 接收端知道完整的消息边界，而不像 read() 可能返回部分数据。 */

namespace ws::mq {
class MessageQueue {
public:
    /**
     * \brief  The default value of message queue size, Root privilege may be required if queue size more than the
     * default size of underlying OS. The default size on Linux is 10.
     *
     */
    static constexpr int32_t kQueueSize{10};

    enum class OpMode : int32_t { kReadOnly, kReadWrite };

    MessageQueue() = default;
    virtual ~MessageQueue() = default;
    DISABLE_COPY_AND_MOVE(MessageQueue);

    [[nodiscard]]
    virtual int32_t Close() const noexcept = 0;

    virtual int32_t Send(char const* msg, uint32_t const priority = 0U) const noexcept = 0;

    virtual int32_t Receive(char* msg, uint32_t& priority) const noexcept = 0;

    /**
     * \brief  queue statu is normal or not.
     *
     * @return true
     * @return false
     */
    [[nodiscard]]
    virtual bool Status() const noexcept = 0;

    /**
     * \brief  Get Number of messages currently queued.
     *
     * @return int64_t >=0 Get Number of messages currently queued.
     * @return int64_t -1 Failed to get Number of messages currently queued.
     */
    [[nodiscard]]
    virtual int64_t CurrentCount() const noexcept = 0;

    /**
     * \brief  Factory function to create MessageQueue instance.
     *
     * \param [dir] mq_name The name of message queue.
     * \param [dir] msg_size The size of message.
     * \param [dir] mode The operation mode of message queue.
     * \param [dir] queue_size The depth of message queue.
     * \param [dir] unlink_on_quit If to destory the message queue on quit.
     *
     * \return A unique pointer of MessageQueue.
     */
    static std::unique_ptr<MessageQueue> Create(std::string const& mq_name, int64_t const msg_size, OpMode const mode,
                                                int32_t const queue_size = kQueueSize,
                                                bool const unlink_on_quit = false) noexcept;
};

}  // namespace ws::mq

#endif  // MQ_MSG_QUEUE_HPP_
