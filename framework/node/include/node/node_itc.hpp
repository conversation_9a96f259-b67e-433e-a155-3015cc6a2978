#ifndef NODE_NODE_ITC_HPP_
#define NODE_NODE_ITC_HPP_

#include <functional>
#include <memory>
#include <string>

#include "com/com_interface.hpp"
#include "type/traites.hpp"
#include "utils/macros.hpp"
#include "utils/mutex_helper.hpp"

namespace ws::node {
// max size of callbacks for single itc topic.
constexpr std::size_t kItcTopicCallbackMax{32UL};

/**
 * \brief  itc communication header structure.
 * @private
 */
struct ItcHeader {
    com::MessageInfo info;   // timestamp & seqsum.
    type::TypeTraits type;   // 消息类型和定义.
    std::string topic;       // 消息topic.
    std::string class_name;  // 发布节点名称.
    int64_t node_tid;        // 发布节点线程id.
    int64_t pipeline_id;     // 消息所属pipeline id.
    bool is_ipc;             // message来源: true: ipc/dds, false: itc.
};

}  // namespace ws::node
