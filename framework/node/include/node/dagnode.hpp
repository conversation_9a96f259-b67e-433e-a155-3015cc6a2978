#ifndef NODE_DAGNODE_HPP_
#define NODE_DAGNODE_HPP_

#include <atomic>

#include "node/node_support.hpp"

namespace ws::node {

/// @brief DagNode接口类，必须调用WS_REGISTER_DAGNODE宏导出符号才可以被加载.
class DagNode {
public:
    /// @brief dlopen()调用加载DagNode动态库时执行.
    /// 用户自定义的DAG回调函数需要在子类构造中调用WS_REGISTER_DAGNODE_CALLBACK宏注册.
    DagNode() = default;

    /// @brief dlclose()调用卸载DagNode动态库时执行.
    virtual ~DagNode() = default;

    /// @brief 初始化接口,必须继承实现，在执行构造函数之后被调用.
    /// @param argc 命令行参数个数.
    /// @param argv 命令行参数数组.
    /// @return 初始化成功返回true,否则返回false.
    virtual bool Init(int argc, char** argv) = 0;

    /// @brief 退出接口,必须继承实现，在执行析构函数之前被调用.
    /// @return 退出成功返回true,否则返回false.
    virtual bool Exit() = 0;

    /// @brief 应用退出接口，调用后整个进程（包含进程其他Node和DagGraph）.
    /// @param reason 退出原因描述.
    void Abort(std::string const& reason) {
        GetNodeName(once_flag_, class_name_, this);
        NodeAbort(class_name_, reason);
    }

    /**
     * \brief 创建消息发布对象.
     *
     * \tparam Message      需要发布的消息类型，跨进程发布的topic必须使用IDL消息类型.
     * \param [dir] topic   消息发布topic名称，不能为空.
     * @return std::shared_ptr<Publisher<Message>>  消息发布对象指针.
     */
    template <typename Message>
    std::shared_ptr<Publisher<Message>> CreatePublisher(std::string const& topic) {
        GetNodeName(once_flag_, class_name_, this);
        return std::make_shared<Publisher<Message>>(class_name_, topic);
    }

    /**
     * \brief  注册和接受DAG图外topic消息.
     *
     * \tparam Message  需要注册的消息类型，跨进程通信的topic必须使用IDL消息类型.
     * \param [dir] topic  消息topic名称，不能为空.
     */
    template <typename Message>
    void RegisterExternTopic(std::string const& topic) {
        RegisterExternTopic<Message>(topic);
    }

    ////////////////// 以下为内部接口 ///////////////////
    /// @private
    DagCallbackHandlePtr RegisterCallback(std : string const& name, DagCallback const& callback) {
        auto handle = std::make_shared<DagCallbackHandle>(name, callback);
        dag_callback_.insert({name, handle});
        return handle;
    }

    /// @private
    DagCallbackHandlePtr QueryCallback(std::string const& name) {
        auto const iter = dag_callback_.find(name);
        if (iter != dag_callback_.end()) {
            return iter->second;
        } else {
            return nullptr;
        }
    }

    /// @private
    bool InvokeCallback(DagCallbackHandlePtr callback, DagMessagePtr const& input) {
        return (this->*(callback->Func()))(input);
    }

    /// @private
    NodeStatus GetStatus() { return node_status_.load(); }

    /// @private
    void SetStatus(NodeStatus const status) { node_status_.store(status); }

private:
    std::once_flag once_flag_;
    std::string class_name_;
    std::atomic<NodeStatus> node_status_{NodeStatus::kUninited};
    std::map<std::string, DagCallbackHandlePtr> dag_callback_;  // callback name -> callback handle.
};

}  // namespace ws::node
