#ifndef NODE_NODE_HPP_
#define NODE_NODE_HPP_

#include <atomic>

#include "node/node_support.hpp"

namespace ws::node {

class Node {
public:
    Node();
    virtual ~Node() = default;

    virtual bool Init(int argc, char** argv) = 0;

    virtual bool Exit() = 0;

    void Abort(std::string const& reson) {
        GetNodeName(once_flag_, class_name_, this);
        NodeAbort(class_name_, reason);
    }

    /**
     * \brief 创建消息发布对象.
     *
     * \tparam Message      需要发布的消息类型，跨进程发布的topic必须使用IDL消息类型.
     * \param [dir] topic   消息发布topic名称，不能为空.
     * @return std::shared_ptr<Publisher<Message>> 创建的发布者对象.
     */
    template <typename Message>
    std::shared_ptr<Publisher<Message>> CreatePublisher(std::string const& topic) {
        GetNodeName(once_flag_, class_name_, this);
        return std::make_shared<Publisher<Message>>(class_name_, topic);
    }

    /**
     * \brief  创建消息发布对象，一般用户不使用此接口，需要动态创建进程外ipc通信的publisher时使用
     *
     * \tparam Message      需要发布的消息类型，跨进程发布的topic必须使用IDL消息类型.
     * \param [dir] topic   消息发布topic名称，不能为空.
     * \param [dir] config  消息发布配置，一般用户不使用此接口，需要动态创建进程外ipc通信的publisher时使用.
     * @return std::shared_ptr<Publisher<Message>> 创建的发布者对象.
     */
    template <typename Message>
    std::shared_ptr<Publisher<Message>> CreatePublisherWithConfig(std::string const& topic, IpcConfig const& config) {
        GetNodeName(once_flag_, class_name_, this);
        return std::make_shared<Publisher<Message>>(class_name_, topic, config);
    }

    /**
     * \brief 创建消息订阅对象.(这个函数要c++ insight一下)
     *
     * \tparam Message      需要订阅的消息类型，跨进程订阅的topic必须使用IDL消息类型.
     * \tparam std::function<void(Message const&)>  消息回调函数类型，需要用户实现.
     * \param [dir] topic   消息订阅topic名称，不能为空.
     * \param [dir] callback  消息回调函数，不能为空.
     * @return std::shared_ptr<Subscriber<Message>> 创建的订阅者对象.
     */
    template <typename Message, typename Callback = std::function<void(Message const&)>>
    std::shared_ptr<Subscriber<Message>> CreateSubscriber(std::string const& topic, callback&& callback) {
        using CallbackWithHeader = std::function<void(Message const&, MessageHeader const&)>;
        GetNodeName(once_flag_, class_name_, this);

        CallbackWithHeader callback_with_header;
        if constexpr (std::is_convertible_v<Callback, CallbackWithHeader>) {
            callback_with_header = callback;
        } else {
            callback_with_header = [callback = std::forward<Callback>(callback)](Message const& msg,
                                                                                 MessageHeader const& header) {
                UNUSED(header);
                callback(msg);
            };
        }
        return std::make_shared<Subscriber<Message>>(class_name_, topic, std::move(callback_with_header), std::nullopt,
                                                     entity_mgr_);
    }

    /**
     * \brief 创建服务端client对象.
     *
     * \tparam Request  请求消息类型.
     * \tparam Response 响应消息类型.
     */
    template <typename Request, typename Response>
    std::shared_ptr<Client<Request, Response>> CreateClient(std::string const& client_name,
                                                            std::string const& server_name) {
        GetNodeName(once_flag_, class_name_, this);
        std::string const node_client_name{class_name_ + kNodeNameDelimiter + client_name};
        return std::make_shared<Client<Request, Response>>(node_client_name, server_name);
    }

    /**
     * \brief 创建服务端service对象.
     *
     * \tparam Request  请求消息类型.
     * \tparam Response 响应消息类型.
     */
    template <typename Request, typename Response>
    std::shared_ptr<Server<Request, Response>> CreateServer(
        std::string const& server_name,
        std::function<int32_t(RpcResult const&, Request const&, Response&)>&& resp_callback) {
        return std::make_shared<Server<Request, Response>>(server_name, std::move(resp_callback));
    }

    /**
     * \brief 创建定时器对象，注意需要调用Start()才会启动定时器.
     *
     * \tparam Rep
     * \tparam Period
     * \param [dir] timer_name 定时器名称，可以按定时器名称配置调度参数，所以需要唯一，避免重名.
     * \param [dir] duration   定时器回调函数执行间隔，可以使用ms等时间单位字面量.
     * \param [dir] func       定时器回调函数
     * @return std::shared_ptr<Timer>
     */
    template <typename Rep, typename Period>
    std::shared_ptr<Timer> CreateTimer(std::string const& timer_name, std::chrono::duration<Rep, Period> duration,
                                       std::function<void()>&& func) {
        GetNodeName(once_flag_, class_name_, this);
        return std::make_shared<Timer>(class_name_, timer_name, duration, std::move(func), entity_mgr_);
    }

    std::shared_ptr<Timer> CreateTimer() {
        GetNodeName(once_flag_, class_name_, this);
        return std::make_shared<Timer>(class_name_, entity_mgr_);
    }

    virtual void OnControlEvent(control::ControlCommand const command);

    //////////// 以下为内部接口 /////////////
    /// @private
    NodeStatus GetStatus();

    /// @private
    void SetStatus(NodeStatus const status);

    /// @private
    void SetNodeUnderControl(std::string const& class_name, std::vector<std::string> const& groups, bool control);

private:
    std::once_flag once_flag_;
    std::string class_name_;
    std::atomic<NodeStatus> node_status_{NodeStatus::kUninited};
    std::shared_ptr<ws::node::control::EntityManager> entity_mgr_{nullptr};
};

}  // namespace ws::node

#endif  // NODE_NODE_HPP_