# Node开发流程：

+ 继承Node类(头文件在 include/node.hpp), 实现虚函数和业务逻辑，编译出libXXXNode.so

+ 注意: app配置文件中的"node_files"要填写实际运行环境中node配置的路径

+ 需要修改的app配置文件项:

    参考示例，以val.app为例：
    ```
    node_files: "/opt/workspace/apps/lios3/install/x86_64_Linux_gnu_debug/test/val_vehicle_status.node" // 运行在同一app_container中的第一个Node节点配置的绝对路径
    node_files: "/opt/workspace/apps/lios3/install/x86_64_Linux_gnu_debug/test/val_gnss.node" // 运行在同一app_container中的第二个Node节点配置的绝对路径
    ```

+ 编写Node的配置文件： \*\*\*.node

    参考示例：
    ```
    name: "val_gnss"        // Node名，可自行指定
    plugin: "ValGnssNode"   // 继承Node类的子类类名，大小写敏感
    lib: "libval.so"        // 编译出来的动态库全名，大小写敏感
    path: "/tmp"            // 实际运行环境中动态库的绝对路径（若无此项或此项为空，则使用app配置中指定的默认路径，暂不支持配置）
    node_params {
        params {
            key: "qos_config_file"
            value: "./qos.xml"
        }
        params {
            key: "val_config_file"
            value: "./val.yaml"
        }
    }
    ```

# 运行

### 需要可执行程序和动态库

+ app_container

### 运行命令

+ 通过app_container加参数方式(参数为app配置)运行：

+ 如： /app_container test/val.app