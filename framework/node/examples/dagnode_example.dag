dag_name: "dag_example"
logging_level: 3
profiling_level: 3

dag_node {
    node_name: "pub_node"
    class_name: "DagNodePublisherExample"
    lib: "/opt/workspace/lios3/install/x86_64_Linux_gnu_debug/test/libdagnode_publisher_example.so"
    publish: ["/dag/topic"]
    callback {
        is_enable: true
        callback_name: "DagNodePublisherExample::OnTimer"
        show_name: "pub_timer"
        timer_interval: 100
    }
    callback {
        is_enable: true
        callback_name: "DagNodePublisherExample::OnExternTopic"
        task_config {
            queue_size: 1
        }
        topic {
            topic_name: "/topic"
            fetch_mode: FETCH_LATEST
            is_trigger: true
            is_required: true
        }
    }
}

dag_node {
    node_name: "sub_node"
    class_name: "DagNodeSubscriberExample"
    lib: "/opt/workspace/lios3/install/x86_64_Linux_gnu_debug/test/libdagnode_subscriber_example.so"
    publish: ["/dag/topic"]
    callback {
        is_enable: true
        callback_name: "DagNodeSubscriberExample::OnMessage"
        show_name: "sub_callback"
        topic {
            topic_name: "/dag/topic"
            fetch_mode: FETCH_LATEST
            is_trigger: true
        }
        task_config {
            execute_mode: EXCLUSIVE
            nice: -10
        }
    }
}