#include "idl_node_test.hpp"
#include "log/log.hpp"
#include "node/node.hpp"
#include "utils/objectpool.hpp"

struct UserStruct {
    int value;
};

class NodePublisherExample : public ws::node::Node {
public:
    NodePublisherExample() = default;
    virtual ~NodePublisherExample() noexcept = default;

    bool Init(int argc, char** argv) override {
        publisher_ = this->CreatePublisher<RtiIdlDataType>("/topic");

        timer_ = this->CreateTimer("timer-name", 1000ms, [this]() {
            // 从对象池中获取消息对象
            std::shared_ptr<RtiIdlDataType> msg = pool_.Acquire();
            msg->value(++seq);

            publisher_->Publish(msg);
        });

        timer_->Start();

        return true;
    }

    void OnControlEvent(ws::node::control::ControlCommand const command) override { Node::OnControlEvent(command); }

    bool Exit() override {
        timer_->Stop();
        return true;
    }

private:
    std::shared_ptr < ws::node::Publisher<RtiIdlDataType> publisher_;
    std::shared_ptr<ws::node::Timer> timer_;
    ws::utils::ObjectPool<RtiIdlDataType> pool_;
    int32_t seq{0};
};

WS_REGISTER_NODE(NodePublisherExample)
