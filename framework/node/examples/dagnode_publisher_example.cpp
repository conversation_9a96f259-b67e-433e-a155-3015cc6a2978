#include "log/log.hpp"
#include "node/dagnode.hpp"
#include "utils/objectpool.hpp"

// DagNode范例，需要配合DAG配置文件运行 dagnode_example.dag

struct UserStruct {
    int value;
};

class DagNodePublisherExample : public ws::node::DagNode {
public:
    explicit DagNodePublisherExample() {
        WS_REGISTER_DAG_CALLBACK(DagNodePublisherExample::OnTimer);
        WS_REGISTER_DAG_CALLBACK(DagNodePublisherExample::OnExternTopic);
    }

    virtual ~DagNodePublisherExample() noexcept = default;

    bool Init(int argc, char** argv) override {
        (void)argc;
        (void)argv;

        publisher_ = this->CreatePublisher<UserStruct>("/dag/topic");

        return true;
    }

    bool OnTimer(ws::node::DagMessagePtr const& message) {
        (void)message;

        std::shared_ptr<UserStruct> msg = pool_.Acquire();
        msg->value = ++seq;
        publisher_->Publish(msg);
        return true;
    }

    bool OnExternTopic(ws::node::DagMessagePtr const& message) {
        (void)message;
        std::shared_ptr<UserStruct> idl_msg = message->Fetch<UserStruct>("/topic");

        return true;
    }

private:
    int32_t seq{0};
    ws::utils::ObjectPool<UserStruct> pool_;
    std::shared_ptr<ws::node::Publisher<UserStruct>> publisher_;
};

WS_REGISTER_DAG_NODE(DagNodePublisherExample)