#include "idl_node_test.hpp"
#include "log/log.hpp"
#include "node/node.hpp"

struct UserStruct {
    int value;
};

class NodeSubscriberExample : public ws::node::Node {
public:
    NodeSubscriberExample() = default;
    virtual ~NodeSubscriberExample() noexcept = default;

    bool Init(int argc, char** argv) override {
        publisher_ = this->CreatePublisher<RtiIdlDataType>("/topic");

        auto callback = [](RtiIdlDataType const& msg) {
            ws::log::Debug("node-example", "subscriber recvv seq: %d", msg.value());
        };

        subscriber_ = this->CreateSubscriber<RtiIdlDataType>("/topic", callback);
        subscriber_->Subscribe();

        // 带message info类型的订阅对象的订阅
        std::function<void(RtiDataType const&, ws::node::MessageHeader const&)> callback2 =
            [](RtiDataType const& msg, ws::node::MessageHeader const& msg_hdr) {
                if (msg_hdr.is_ipc) {
                    ...
                } else {
                    ...
                }
            };
        subscriber_2 = this->CreateSubscriber<RtiIdlDataType>("/topic", std::move(callback2));
        subscriber_2->Subscribe();

        return true;
    }

    void OnControlEvent(ws::node::control::ControlCommand const command) override { Node::OnControlEvent(command); }

    bool Exit() override {
        subscriber_->UnSubscribe();
        subscriber_2->UnSubscribe();
        return true;
    }

private:
    std::shared_ptr<ws::node::Subscriber<RtiIdlDataType>> subscriber_;
    std::shared_ptr<ws::node::Subscriber<RtiIdlDataType, FunctionWithHdr<RtiIdlDataType>>> subscriber_2;
};

WS_REGISTER_NODE(NodeSubscriberExample)
