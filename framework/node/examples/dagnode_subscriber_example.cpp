#include "log/log.hpp"
#include "node/dagnode.hpp"

// DagNode范例，需要配合DAG配置文件运行 dagnode_example.dag

struct UserStruct {
    int value;
};

class DagNodeSubscriberExample : public ws::node::DagNode {
public:
    explicit DagNodeSubscriberExample() { WS_REGISTER_DAG_CALLBACK(DagNodeSubscriberExample::OnMessage); }

    virtual ~DagNodeSubscriberExample() noexcept = default;

    bool Init(int argc, char** argv) override {
        (void)argc;
        (void)argv;

        return true;
    }

    bool Exit() override { return true; }

    bool OnMessage(ws::node::DagMessagePtr const& message) {
        std::shared_ptr<UserStruct> msg = message->Fetch<UserStruct>("/dag/topic");
        return true;
    }
};

WS_REGISTER_DAG_NODE(DagNodeSubscriberExample)