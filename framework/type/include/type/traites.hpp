#ifndef TYPE_TRAITES_HPP_
#define TYPE_TRAITES_HPP_

#include <cstdint>
#include <string>
#include <type_traits>

#include "type/cereal_internal.hpp"

namespace ws::type {

enum class MessageType : int8_t {
    kUnknown,
    kInteger,       // all integer types
    kDecimal,       // float and double
    kString,        // std::string
    kBuffer,        // serialized buffer (std::vector<char>)
    kSerializable,  // serializable message
    // kRtiIdl,        // rti idl topic
    // kLiddsIdl,      // lidds idl topic
    // kProtobuf,      // google protobuf
};

struct TypeTraits {
    MessageType type;
    std::string idl_define;
    std::string idl_type;
};

/**
 * extract integer type traits.
 */
template <typename T, std::enable_if_t<std::is_integral<T>::value, bool> = true>
TypeTraits ExtractTraits(T const& t) {
    return TypeTraits{MessageType::kInteger, {}, {}};
}

/**
 * extract float/double type traits.
 */
template <typename T, std::enable_if_t<std::is_floating_point<T>::value, bool> = true>
TypeTraits ExtractTraits(T const& t) {
    return TypeTraits{MessageType::kDecimal, {}, {}};
}

/**
 * extract serializable message traits.
 * T serializeable message type.
 */
template <typename T, std::enable_if_t<std::is_base_of_v<type::Serializable, T>, bool> = true>
TypeTraits ExtractTraits(T const& t) {
    return TypeTraits{MessageType::kSerializable, {}, {}};
}

/**
 * extract string type traits.
 */
TypeTraits ExtractTraits(std::string const& t);

/**
 * extract buffer type traits.
 */
TypeTraits ExtractTraits(Buffer const& t);

}  // namespace ws::type

#endif  // TYPE_TRAITES_HPP_
