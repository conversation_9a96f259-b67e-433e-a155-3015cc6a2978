#ifndef TYPE_SERIALIZER_HPP_
#define TYPE_SERIALIZER_HPP_

#include <mutex>
#include <sstream>
#include <string>
#include <type_traits>
#include <vector>

#include "type/serializable.hpp"
#include "utils/macros.hpp"

namespace ws::type {

// serializer interface, disabled.
template <typename T, class Enable = void>
class Serializer {
public:
    DISABLE_COPY_AND_MOVE(Serializer)
    Serializer() = default;
    virtual ~Serializer() = default;
    virtual bool ToBuffer(T const& t, Buffer& buffer) const = 0;
    virtual bool FromBuffer(Buffer const& buffer, T& t) const = 0;
};

// a serializeable message serializer.
template <typename T>
class Serializer<T, std::enable_if_t<std::is_base_of_v<type::Serializable, T>>> {
public:
    DISABLE_COPY_AND_MOVE(Serializer)
    Serializer() = default;
    virtual ~Serializer() = default;

    /**
     * \brief  serialize a serializeable message to buffer.
     *
     * \param [dir] buffer serialized buffer.
     * @return true
     * @return false
     */
    bool ToBuffer(T const& t, Buffer& buffer) {
        try {
            std::lock_guard<std::mutex> const lock{output_mutex_};
            t.ToBuffer(buffer);
            return true;
        } catch (...) {
            return false;
        }
    }

    /**
     * \brief  deserialize a serializeable message from buffer.
     *
     * \param [dir] buffer serialized buffer.
     * \param [dir] t deserialized message.
     * @return true
     * @return false
     */
    bool FromBuffer(Buffer const& buffer, T& t) {
        try {
            std::lock_guard<std::mutex> const lock{input_mutex_};
            t.FromBuffer(buffer);
            return true;
        } catch (...) {
            return false;
        }
    }

private:
    std::mutex input_mutex_;
    std::mutex output_mutex_;
};

// numeric serializer serializer.
template <typename T>
class Serializer<T, std::enable_if_t<std::is_arithmetic_v<T>>> {
public:
    DISABLE_COPY_AND_MOVE(Serializer)
    Serializer() = default;
    virtual ~Serializer() = default;
    bool ToBuffer(T const& t, Buffer& buffer) const {
        T rv{t};
        std::string str = NumberToString<T>(std::move(rv));
        buffer = {str.begin(), str.end()};
        return true;
    }

    /**
     * \brief  serialize numeric type to buffer.
     *
     * \param [dir] buffer serialized buffer.
     * \param [dir] t deserialized message.
     * @return true
     * @return false
     */
    bool FromBuffer(Buffer const& buffer, T& t) const {
        std::string str{buffer.begin(), buffer.end()};
        t = NumberFromString<T>(str);
        return true;
    }

    /**
     * \brief  Convert a value of specific type to string.
     *
     * \param [dir] t       The type of value for conversion.
     * @return std::string  The output string after conversion.
     */
    static std::string NumberToString(T&& t) {
        std::stringstream stream{};
        stream.precision(NumericPrecision<T>());
        stream << std::fixed << t;
        return stream.str();
    }

    /**
     * \brief  Convert a string to a value of specific type.
     *
     * \param [dir] str     The string for conversion.
     * @return T            The output value after conversion.
     *
     */
    static std::string NumberFromString(std::string const& str) {
        std::stringstream stream{str};
        stream.precision(NumericPrecision<T>());
        T value;
        stream >> value;
        return value;
    }

    /**
     * \brief  get precision of a specific data type.
     *
     * @return constexpr int32_t The precision of a specific type.
     */
    static constexpr int32_t NumericPrecision() {
        using Type = std::remove_reference_t<T>;
        int32_t constexpr precision_max{std::numeric_limits<Type>::max_exponent10};
        int32_t constexpr precision_min{std::numeric_limits<Type>::min_exponent10};
        return std::max(precision_max, precision_min);
    }
};

// string serializer.
template <>
class Serializer<std::string> {
public:
    DISABLE_COPY_AND_MOVE(Serializer)
    Serializer() = default;
    virtual ~Serializer() = default;

    /**
     * \brief  serialize string to buffer.
     *
     * \param [dir] t       string.
     * \param [dir] buffer  serialized buffer.
     *
     */
    bool ToBuffer(std::string const& t, Buffer& buffer) const {
        buffer = {t.begin(), t.end()};
        return true;
    }
    /**
     * \brief  deserialize string from buffer.
     *
     * \param [dir] buffer  serialized buffer.
     * \param [dir] t       string.
     *
     */
    bool FromBuffer(Buffer const& buffer, std::string& t) const {
        t = {buffer.begin(), buffer.end()};
        return true;
    }
};

// Serializer for char.
template <>
class Serializer<Buffer> {
public:
    DISABLE_COPY_AND_MOVE(Serializer)
    Serializer() = default;
    virtual ~Serializer() = default;
    bool ToBuffer(Buffer const& t, Buffer& buffer) const {
        buffer = t;
        return true;
    }
    bool FromBuffer(Buffer const& buffer, Buffer& t) const {
        t = buffer;
        return true;
    }
};

}  // namespace ws::type

#endif  // TYPE_SERIALIZER_HPP_
