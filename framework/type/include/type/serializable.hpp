#ifndef TYPE_SERIALIZABLE_HPP_
#define TYPE_SERIALIZABLE_HPP_

#define CEREAL_THREAD_SAFE 1

// for c++ std type
#include <cereal/types/array.hpp>
#include <cereal/types/atomic.hpp>
#include <cereal/types/base_class.hpp>
#include <cereal/types/bitset.hpp>
#include <cereal/types/boost_variant.hpp>
#include <cereal/types/chrono.hpp>
#include <cereal/types/common.hpp>
#include <cereal/types/complex.hpp>
#include <cereal/types/deque.hpp>
#include <cereal/types/forward_list.hpp>
#include <cereal/types/functional.hpp>
#include <cereal/types/list.hpp>
#include <cereal/types/map.hpp>
#include <cereal/types/memory.hpp>
#include <cereal/types/optional.hpp>
#include <cereal/types/polymorphic.hpp>
#include <cereal/types/queue.hpp>
#include <cereal/types/set.hpp>
#include <cereal/types/stack.hpp>
#include <cereal/types/string.hpp>
#include <cereal/types/tuple.hpp>
#include <cereal/types/unordered_map.hpp>
#include <cereal/types/unordered_set.hpp>
#include <cereal/types/utility.hpp>
#include <cereal/types/valarray.hpp>
#include <cereal/types/variant.hpp>
#include <cereal/types/vector.hpp>

// for seriablize
#include <cereal/archives/portable_binary.hpp>
#include <sstream>

namespace ws::type {

using Buffer = std::vector<char>;
void SstreamToBuffer(std::stringstream& ss, Buffer& buffer);
void BufferToSstream(Buffer const& buffer, std::stringstream& ss);

class Serializable {
public:
    virtual ~Serializable() = default;
    /**
     * \brief  Serialize data to buffer.
     *
     * \param [dir] buffer Serialized buffer.
     */
    virtual void ToBuffer(Buffer& buffer) const = 0;

    /**
     * \brief  Deserialize data from buffer.
     *
     * \param [dir] buffer Serialized buffer.
     */
    virtual void FromBuffer(Buffer const& buffer) = 0;
};

/**
 * \brief  Generate cereal archive template and convert functions.
 *
 */
#define SERIALIZABLE(...)                                     \
    template <class Archive>                                  \
    void serialize(Archive& archive) {                        \
        archive(__VA_ARGS__);                                 \
    }                                                         \
                                                              \
public:                                                       \
    void ToBuffer(type::Buffer& buffer) const override {      \
        std::stringstream ss{};                               \
        {                                                     \
            cereal::PortableBinaryOutputArchive oarchive{ss}; \
            oarchive(*this);                                  \
        }                                                     \
        type::SstreamToBuffer(ss, buffer);                    \
    }                                                         \
    void FromBuffer(type::Buffer const& buffer) override {    \
        std::stringstream ss{};                               \
        type::BufferToSstream(buffer, ss);                    \
        {                                                     \
            cereal::PortableBinaryInputArchive iarchive{ss};  \
            iarchive(*this);                                  \
        }                                                     \
    }

}  // namespace ws::type

#endif  // TYPE_SERIALIZABLE_HPP_