#include <iostream>
#include <type_traits>

// 仅当 T 是整数类型时，该模板才会匹配

// std::is_integral<T>::value：这是一个类型萃取（type trait），用于判断 T 是否是一个整数类型（如 int、long、char
// 等）。如果 T 是整数类型，则 std::is_integral<T>::value 为 true，否则为 false
// std::enable_if_t<B, T> 是 std::enable_if<B, T>::type 的简写，它只有在 B 为 true 时才会定义 type，否则会导致编译失败。
// 这里 B 是 std::is_integral<T>::value，即 T 必须是整数类型，否则模板不会匹配。
// 第二个模板参数 bool 设定 enable_if_t 的结果为 bool 类型，并默认为 true。
template <typename T, std::enable_if_t<std::is_integral<T>::value, bool> = true>
void printType(T value) {
    std::cout << "Integer type: " << value << std::endl;
}

// 另一种使用方式（用 std::enable_if_t 作为返回值）：
// template <typename T>
// std::enable_if_t<std::is_integral<T>::value, void> printType(T value) {
//     std::cout << "Integer type: " << value << std::endl;
// }

int main() {
    printType(42);  // 正常调用，42 是 int 类型
    // printType(3.14);  // 编译错误，double 不是整数类型
}