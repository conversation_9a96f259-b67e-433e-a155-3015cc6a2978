#include "type/serializable.hpp"
#include "type/traites.hpp"

namespace ws::type {

// type traits for std::string
TypeTraits ExtractTraits(std::string const& t) {
    (void)t;
    return TypeTraits{MessageType::kString, {}, {}};
}

// type traits for std::vector<char>
TypeTraits ExtractTraits(Buffer const& t) {
    (void)t;
    return TypeTraits{MessageType::kBuffer, {}, {}};
}

// efficient version of buffer/sstream convertion.
void SstreamToBuffer(std::stringstream& ss, Buffer& buffer) {
    // get stream buffer size.
    ss.seekg(0, std::ios::beg);
    auto const bof = ss.tellg();
    ss.seekg(0, std::ios::end);
    auto const ss_size = static_cast<std::size_t>(ss.tellg() - bof);
    ss.seekg(0, std::ios::beg);

    buffer.resize(ss_size);
    ss.read(reinterpret_cast<char*>(buffer.data()), static_cast<std::streamsize>(buffer.size()));
}

void BufferToSstream(Buffer const& buffer, std::stringstream& ss) {
    ss.write(reinterpret_cast<char const*>(buffer.data()), static_cast<std::streamsize>(buffer.size()));
}

}  // namespace ws::type