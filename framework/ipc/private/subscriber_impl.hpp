#ifndef IPC_SUBSCRIBER_IMPL_HPP_
#define IPC_SUBSCRIBER_IMPL_HPP_

#include "concurrent/task_executor.hpp"  // 就是一个带任务等级的线程池
#include "dispatcher.hpp"
#include "routing.hpp"
#include "type/serializable.hpp"
#include "utils/macros.hpp"

namespace ws::ipc {

constexpr auto kIpcSubLog{"ipc-sub"};

class IpcSubscriberImpl {
public:
    using IpcTopicCallback = std::function<void(BufferPtr const&, MessageInfo const* info)>;

    IpcSubscriberImpl(int32_t const domain_id, std::string const& topic_name, type::TypeTraits const& topic_type,
                      IpcTopicCallback&& callback);
    virtual ~IpcSubscriberImpl();
    DISABLE_COPY_AND_MOVE(IpcSubscriberImpl)
    void Subscribe();
    void Unsubscribe();

private:
    NodeInfo self_{};
    TopicInfo topic_info_{};
    IpcTopicCallback topic_callback_{};
    std::mutex executor_mutex_{};
    std::unique_ptr<concurrent::TaskExecutor> executor_{nullptr};
    std::unique_ptr<concurrent::TaskCreator> creator_{nullptr};
};

}  // namespace ws::ipc
