#ifndef IPC_PUBLISHER_IMPL_HPP_
#define IPC_PUBLISHER_IMPL_HPP_

#include "dispatcher.hpp"
#include "routing.hpp"
#include "type/serializable.hpp"
#include "utils/macros.hpp"

namespace ws::ipc {

constexpr auto kIpcPubLog{"ipc-pub"};

class IpcPublisherImpl {
public:
    IpcPublisherImpl(int32_t const domain_id, std::string const& topic_name, type::TypeTraits const& topic_type);
    virtual ~IpcPublisherImpl();
    DISABLE_COPY_AND_MOVE(IpcPublisherImpl)
    void Publish(BufferPtr const& buffer);

private:
    NodeInfo self_{};
    TopicInfo topic_info_{};
    std::atomic<int64_t> seqnum_{0};
};

}  // namespace ws::ipc

#endif  // IPC_PUBLISHER_IMPL_HPP_