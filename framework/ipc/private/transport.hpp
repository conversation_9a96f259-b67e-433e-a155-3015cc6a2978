#ifndef IPC_TRANSPORT_HPP_
#define IPC_TRANSPORT_HPP_

#include "protocol.hpp"

namespace ws::ipc {

// Transport以单例模式创建，在进程生命周期内有效，为避免未使用IPC的进程的额外负担，要求Transport的构造函数尽量避免消耗资源操作

// Transport传输层实现订阅回调函数，MessageEnvelopePtr为消息智能指针，无返回值
using TransportTopicCallback = std::function<void(MessageEnvelopePtr const&)>;

// Transport传输层异步返回结果回调函数，TransportResponseCallback为消息智能指针，无返回值
using TransportResponseCallback = std::function<void(ResponseEnvelopePtr const&)>;

// Transport传输层实现异步请求回调函数，TransportRequestCallback为消息智能指针，无返回值
using TransportRequestCallback = std::function<void(RequestEnvelopePtr const&, TransportResponseCallback&&)>;

class TransportInterface {
public:
    virtual ~TransportInterface() = default;

    virtual std::string Name() const = 0;

    virtual void Shutdown() = 0;

    virtual void OnRoutingEvent(RoutingEvent const& event) = 0;

    /////////////// pub/sub interface ////////////////

    // 注册Publisher
    virtual void RegisterPublisher(TopicInfo const& topic, NodeInfo& publisher) = 0;

    // 注销Publisher和topic
    virtual void DeRegisterPublisher(TopicInfo const& topic, NodeInfo const& publisher) = 0;

    // publisher发布消息
    virtual void Publish(TopicInfo const& topic, NodeInfo const& publisher, MessageEnvelopePtr const& message) = 0;

    // 注册Subscriber，查询Publisher，指定订阅回调函数
    virtual void RegisterSubscriber(TopicInfo const& topic, NodeInfo& subscriber, NodeList const& publishers,
                                    TransportTopicCallback&& callback) = 0;

    // 注销Subscriber
    virtual void DeRegisterSubscriber(TopicInfo const& topic, NodeInfo const& subscriber) = 0;

    // Subscriber启动/恢复订阅
    virtual void Subscribe(TopicInfo const& topic, NodeInfo const& subscriber) = 0;

    // Subscriber停止订阅
    virtual void UnSubscribe(TopicInfo const& topic, NodeInfo const& subscriber) = 0;

    ///////////////////// req/res interface(RPC) ////////////////

    // 注册Server和Service，查询Client，diff-host类型Transport需要分配Service端口
    virtual void RegisterServer(std::string const& service_id, NodeInfo& server, NodeList const& clients) = 0;

    // 注销Server和Service
    virtual void DeRegisterServer(std::string const& service_id, NodeInfo const& server) = 0;

    // Server指定请求回调函数并启动服务
    virtual void AsyncRequest(std::string const& service_id, std::string const& client_id, NodeInfo& client,
                              NodeInfo const& server) = 0;

    // Server停止接收请求
    virtual void StopReceiveRequests(std::string const& service_id, NodeInfo const& server,
                                     TransportRequestCallback&& callback) = 0;

    // 注册CLient,查询Server
    virtual void RegisterClient(std::string const& service_id, std::string const& client_id, NodeInfo& client,
                                NodeList const& server) = 0;

    // 注销Client
    virtual void DeRegisterClient(std::string const& service_id, std::string const& client_id,
                                  NodeInfo const& client) = 0;

    // CLient同步提交请求（异步提交请求模式基于同步提交版本封装）
    virtual void SyncRequest(std::string const& service_id, std::string const& client_id, NodeInfo const& client,
                             NodeInfo const& server, RequestEnvelopePtr const& request, ResponseEnvelopePtr& response,
                             std::chrono::nanoseconds const& expiry_ns) = 0;
};

}  // namespace ws::ipc

#endif  // IPC_TRANSPORT_HPP_
