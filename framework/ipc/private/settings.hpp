#ifndef IPC_SETTINGS_HPP_
#define IPC_SETTINGS_HPP_

#include <string>
namespace ws::ipc {

constexpr int64_t kNsToMs{1000000};  // ns to ms ratio.

// const settings.
constexpr int32_t kTopicTaskWorkers{1};     // subscriber workers.
constexpr int32_t kRequestTaskWorkers{1};   // client async request workers.
constexpr int32_t kResponseTaskWorkers{1};  // server sync response workers.

constexpr auto kTopicTaskPrefix{"c:"};     // c: topi[C] callback.
constexpr auto kRequestTackPrefix{"q:"};   // q: re[Q]uest callback.
constexpr auto kResponseTaskPrefix{"p:"};  // p: res[P]onse callback.

// heartbeat.
constexpr int32_t kHeartbeatIntervalMs{1000};  // 节点心跳间隔 ms.
constexpr int32_t kHeartbeatTimeoutMs{5000};   // 节点心跳超时 ms. 超过未收到心跳视为节点下线.

constexpr int32_t kTopicIntervalMs{1000};    // Topic心跳间隔 ms.
constexpr int32_t kTopicThreshouldMs{5000};  // Topic心跳超时 ms. 超过未更新心跳视为订阅中断.

// shm settings.
constexpr int32_t kShmMessageSizeMax{16};  // 共享内存通信模式消息最大长度. 单位:MB.

// zmq settings.
struct ZmqSettings {
    std::string kDefaultNetwork{"172.31.3"};     // 默认IP网段范围，可以通过环境变量LIOS_IPC_NETWORK设置.
    std::string kEventAddr{"***********:7777"};  // 路由心跳事件广播端口号.

    int32_t kIoThreads{1};        // ZMQ后台IO线程数量.
    int32_t kPollWaitMs{10};      // ZMQ多路服用等待循环触发间隔，单位ms(设置为0即忙等待，会增加CPU占用)
    int32_t kConnectWaitMs{100};  // ZMQ connect/send/recv操作等待超时，单位ms.
    int32_t kCloseWaitMs{0};      // ZMQ断开链接后等待清理事件，单位ms.
    int32_t kBufferSize{1000};    // ZMQ发送/接收数据队列缓存数量.
    int32_t kRetryLimit{3};       // ZMQ收发数据EAGAIN错误重试次数.
};

}  // namespace ws::ipc

#endif  // IPC_SETTINGS_HPP_