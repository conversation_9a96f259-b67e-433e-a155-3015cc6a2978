#ifndef IPC_CLIENT_IMPL_HPP_
#define IPC_CLIENT_IMPL_HPP_

#include "concurrent/task_executor.hpp"  // 就是一个带任务等级的线程池
#include "dispatcher.hpp"
#include "routing.hpp"
#include "type/serializable.hpp"
#include "utils/macros.hpp"

namespace ws::ipc {

constexpr auto kIpcClientLog{"ipc-client"};
class IpcClientImpl {
public:
    using IpcResponseCallback = std::function<void(RequestStatus const&, BufferPyr const&)>;
    IpcClientImpl(int32_t const domain_id, std::string const& client_id, std::string const& service_id);
    virtual ~IpcClientImpl();
    DISABLE_COPY_AND_MOVE(IpcClientImpl)
    void AsyncRequest(BufferPtr const& request_buffer, IpcResponseCallback&& callback,
                      std::chrono::nanoseconds const& expiry_ns);

    RequestStatus SyncRequest(BufferPtr const& request_buffer, BufferPtr& response_buffer,
                              std::chrono::nanoseconds const& expiry_ns);

private:
    NodeInfo self_{};
    std::string client_id_;
    std::string service_id_;
    std::atomic<int64_t> seqnum_{0};
    std::map<ServicesStatusType, RequestStatus> request_status_{};
    IpcResponseCallback response_callback_{};
    std::mutex executor_mutex_{};
    std::unique_ptr<concurrent::TaskExecutor> executor_{nullptr};
    std::unique_ptr<concurrent::TaskCreator> creator_{nullptr};
};

}  // namespace ws::ipc
