#ifndef IPC_DISPATCHER_HPP_
#define IPC_DISPATCHER_HPP_

#include <set>

#include "protocol.hpp"
#include "transport.hpp"
#include "utils/mutex_helper.hpp"

namespace ws::ipc {

constexpr auto kIpcDispatcherLog{"ipc-dispatcher"};

constexpr auto kEnvIpcTransport{"IPC_TRANSPORT"};
constexpr auto kTransportSameProc{"same-proc"};
constexpr auto kTransportSameHost{"same-host"};
constexpr auto kTransportDiffHost{"diff-host"};

enum class TransportMode : int8_t {
    kAuto = 0,
    kSameProc = 0,
    kSameHost = 1,
    kDiffHost = 2,
};

// dispatcher interface
class TransportDispatcher {
public:
    static TransportDispatcher& Instance() {
        static TransportDispatcher* instance = new TransportDispatcher;
        return *instance;
    }

    DISABLE_COPY_AND_MOVE(TransportDispatcher)

    // common
    void OnRoutingEvent(RoutingEvent const& routing_event);
    void OnRoutingGraph(GraphInfo const& graph_info);

    // publisher
    void RegisterPublisher(TopicInfo const& topic, NodeInfo& publisher);
    void DeregisterPublisher(TopicInfo const& topic, NodeInfo const& publisher);
    void EnablePublisher(TopicInfo const& topic, NodeList const& subscribers);
    void Publish(TopicInfo const& topic, NodeInfo const& publisher, MessageEnvelopePtr const& message);

    // subscriber
    void RegisterSubscriber(TopicInfo const& topic, NodeInfo& subscriber, NodeList const& publishers,
                            TransportTopicCallback&& callback);
    void DeregisterSubscriber(TopicInfo const& topic, NodeInfo const& subscriber);
    void Subscribe(TopicInfo const& topic, NodeInfo const& subscriber);
    void UnSubscribe(TopicInfo const& topic, NodeInfo const& subscriber);

    // server
    void RegisterServer(std::string const& service_id, NodeInfo& server, NodeList const& clients);
    void DeregisterServer(std::string const& service_id, NodeInfo const& server);
    void AsyncResponses(std::string const& service_id, NodeInfo const& server, TransportRequestCallback&& callback);
    void StopReceiveRequests(std::string const& service_id, NodeInfo const& server);

    // client
    void RegisterClient(std::string const& service_id, std::string const& client_id, NodeInfo& client,
                        NodeInfo const& server);
    void DeregisterClient(std::string const& service_id, std::string const& client_id, NodeInfo const& client);
    void SelectServer(std::string const& service_id, NodeInfo const& server);
    void SyncRequest(std::string const& service_id, std::string const& client_id, NodeInfo const& client,
                     RequestEnvelopePtr& request, ResponseEnvelopePtr& response,
                     std::chrono::nanoseconds const& expiry_ns);

private:
    TransportDispatcher();
    virtual ~TransportDispatcher();

    void GetDebugTransport();
    TransportMode GetAdaptiveTransport(NodeInfo const& node) const;
    static bool IsNodeAdaptive(NodeInfo const& node1, NodeInfo const& node2, TransportMode const mode);

    std::string self_addr_{};
    int64_t self_proc_{0};
    std::map<TransportMode, std::string> transport_name_{};
    std::map<std::string, TransportMode> transport_mode_{};

    // for debug only.
    TransportMode debug_mode_{TransportMode::kAuto};
    TransportInterface* debug_transport_{nullptr};
    // transport mode -> transport handle
    std::map<TransportMode, TransportInterface*> transports_{};

    // routing event filter.
    utils::MutexHelper<std::set<std::string>> published_;   // topic list.
    utils::MutexHelper<std::set<std::string>> subscribed_;  // topic list.
    utils::MutexHelper<std::set<std::string>> requested_;   // service-id list.
    utils::MutexHelper<std::set<std::string>> responded_;   // service-id list.

    ////////////////////////////////////////
    // transport mode -> transport enable status.
    using PublishTransportModes = std::map<TransportMode, bool>;
    // topic -> enable publisher transports.
    utils::MutexHelper<std::unordered_map<std::string, PublishTransportModes>> publish_transports_;

    struct RequestTransport {
        TransportInterface* transport;
        NodeInfo server;
    };

    // service-id -> request transport.
    utils::MutexHelper<std::unordered_map<std::string, RequestTransport>> request_transports_;
};

}  // namespace ws::ipc

#endif  // IPC_DISPATCHER_HPP_