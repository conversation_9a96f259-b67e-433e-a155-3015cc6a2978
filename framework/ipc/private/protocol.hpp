#ifndef IPC_PROTOCOL_HPP_
#define IPC_PROTOCOL_HPP_

#include <map>
#include <memory>
#include <string>

#include "ipc/ipc_define.hpp"
#include "type/serializable.hpp"
#include "type/traites.hpp"

namespace ws::ipc {

/////////////////////////////////////////////////////////////////////////////
// routing protocol define.

/////////////////////////////////////////////////////////////////////////////
// node
enum class RoleType : int8_t {
    kNone = 0,        // default role.
    kPublisher = 1,   // publisher role.
    kSubscriber = 2,  // subscriber role.
    kClient = 3,      // client role.
    kServer = 4,      // server role.
};

struct NodeInfo : public type::Serializable {
    // node info.
    std::string id{};  // node id: role-name-pid-tid-ip-seqnum.
    std::string ip{};  // host-ip
    int64_t pid{0};    // process id
    int64_t tid{0};    // thread id
    RoleType role{RoleType::kNone};
    std::string label{};  // topic published or subscribed. service-id requested or responsed.
    int32_t port{0};      // port of topic or service.
    // create and heartbeat timestamp.
    int64_t create_timestamp_ms{0};
    int64_t heartbeat_timestamp_ms{0};
    // serializable fields.
    SERIALIZABLE(id, ip, pid, tid, role, label, port, create_timestamp_ms, heartbeat_timestamp_ms);
};
using NodeList = std::vector<NodeInfo>;

/////////////////////////////////////////////////////////////////////////////
// topic
struct TopicInfo : public type::Serializable {
    // topic info.
    std::string name{};                                   // topic name.
    type::MessageType type{type::MessageType::kUnknown};  // topic type.
    std::string idl_define{};                             // only for MessageType::kRtiIdl and MessageType::kLiddsIdl.
    std::string idl_type{};                               // only for MessageType::kRtiIdl and MessageType::kLiddsIdl.
    // topic status
    int64_t total_published{0};  // all messages published.
    int64_t last_publish_ns{0};  // last published in nanoseconds.
    NodeInfo last_publisher{};   // last publisher.
    // serializable fields.
    SERIALIZABLE(name, type, idl_define, idl_type, total_published, last_publish_ns, last_publisher);
};

/////////////////////////////////////////////////////////////////////////////
// graph
struct GraphInfo : public type::Serializable {
    std::map<std::string, TopicInfo> topics{};                             // topic -> type, publisher
    std::map<std::string, std::map<std::string, NodeInfo>> publishers{};   // topic -> node-id -> publishers
    std::map<std::string, std::map<std::string, NodeInfo>> subscribers{};  // topic -> node-id -> subscribers
    std::map<std::string, NodeInfo> servers{};                             // service -> server
    std::map<std::string, std::map<std::string, NodeInfo>> clients{};      // service -> node-id -> clients
    // serializable fields.
    SERIALIZABLE(topics, publishers, subscribers, servers, clients);
};

/////////////////////////////////////////////////////////////////////////////
// routing
enum class EventType : int8_t {
    kHeartbeat = 0,   // node heartbeat.
    kOnline = 1,      // node online.
    kOffline = 2,     // node offline.
    kTopicAlive = 3,  // topic alive.
};

struct RoutingEvent : public type::Serializable {
    EventType event{EventType::kHeartbeat};  // broadcas event info.
    NodeInfo node{};                         // broadcast node info.
    TopicInfo topic{};                       // broadcast topic info.
    // serializable fields.
    SERIALIZABLE(event, node, topic);
};

////////////////////////////////////////
// transport protocol define.

/////////////////////////////////////////////////////////////////////////////
// topic
struct MessageHeader : public type::Serializable {
    int64_t seqnum{0};                // message sequence number.
    int64_t publish_timestamp_ns{0};  // message publish time in nanoseconds.
    int64_t send_timestamp_ms{0};     // message receive time in milliseconds.
    std::string topic{};              // message topic name.
    NodeInfo publisher{};             //    message publisher.
    // serializable fields.
    SERIALIZABLE(seqnum, publish_timestamp_ns, send_timestamp_ms, topic, publisher);
};

struct MessageEnvelope : public type::Serializable {
    MessageHeader header{};      // message header.
    BufferPtr payload{nullptr};  // message payload.
    // serializable fields.
    SERIALIZABLE(header, payload);
};
using MessageEnvelopePtr = std::shared_ptr<MessageEnvelope>;

/////////////////////////////////////////////////////////////////////////////
// request
struct RequestHeader : public type::Serializable {
    int64_t seqnum{0};             // request sequence number.
    int64_t send_timestamp_ns{0};  // request send time in nanoseconds.
    std::string service_id{};      // requested service name.
    std::string client_id{};       // request client id.
    NodeInfo server{};             // request server.
    NodeInfo client{};             // request client.
    // serializable fields.
    SERIALIZABLE(seqnum, send_timestamp_ns, service_id, client_id, server, client);
};

struct RequestEnvelope : public type::Serializable {
    RequestHeader header{};      // request header.
    BufferPtr payload{nullptr};  // request payload.
    // serializable fields.
    SERIALIZABLE(header, payload);
};
using RequestEnvelopePtr = std::shared_ptr<RequestEnvelope>;

/////////////////////////////////////////////////////////////////////////////
// response
enum class ServiceStatusType : int8_t {
    kSuccess = 0,   // request succ.
    kFail = 1,      // service callback fail.
    kNotFound = 2,  // service not found.
    kTimeout = 3,   // request timeout.
    kError = 4,     // ipc internal error.
};

struct ResponseHeader : public type::Serializable {
    int64_t seqnum{0};                                       // request(not response) sequence number.
    int64_t request_timestamp_ns{0};                         // request time in nanoseconds.
    int64_t response_timestamp_ns{0};                        // response time in nanoseconds.
    std::string service_id{};                                // requested service name.
    std::string client_id{};                                 // request client id.
    NodeInfo server{};                                       // request server.
    NodeInfo client{};                                       // request client.
    ServiceStatusType status{ServiceStatusType::kNotFound};  // service status.
    // serializable fields.
    SERIALIZABLE(seqnum, request_timestamp_ns, response_timestamp_ns, service_id, client_id, server, client, status);
};

struct ResponseEnvelope : public type::Serializable {
    ResponseHeader header{};     // response header.
    BufferPtr payload{nullptr};  // response payload.
    // serializable fields.
    SERIALIZABLE(header, payload);
};
using ResponseEnvelopePtr = std::shared_ptr<ResponseEnvelope>;

}  // namespace ws::ipc

#endif  // IPC_PROTOCOL_HPP_