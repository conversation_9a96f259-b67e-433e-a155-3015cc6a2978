#ifndef IPC_SUBSCRIBER_HPP_
#define IPC_SUBSCRIBER_HPP_

#include "ipc/ipc_define.hpp"
#include "ipc/subscriber.hpp"
#include "type/serializer.hpp"
#include "type/traits.hpp"

namespace ws::ipc {

// typeless bridge
class IpcSubscriberTypeless {
public:
    using IpcTopicCallback = std::function<void(BufferPtr const& buffer, MessageInfo const*)>;

    explicit IpcSubscriberTypeless(int32_t const domain_id, std::string const& topic_name,
                                   type::TypeTraits const& topic_type, IpcTopicCallback const& callback);
    virtual ~IpcSubscriberTypeless();
    void Subscribe();
    void Unsubscribe();

private:
    void* pimpl_{nullptr};
}

template <typename Message, typename Callback = std::function<void(Message const&)>>
class IpcSubscriber : public ipc::Subscriber<Message, Callback> {
public:
    // callback 1: by reference.
    using RawCallback = std::function<void(Message const&)>;
    // callback 2: by reference & header info.
    using RawCallbackWithInfo = std::function<void(Message const&, MessageInfo const*)>;
    // callback 3: by pointer.
    using NodeCallback = std::function<void(std::shared_ptr<Message> const&)>;
    // callback 4: by pointer & header info.
    using NodeCallbackWithInfo = std::function<void(std::shared_ptr<Message> const&, MessageInfo const*)>;

    // 用于判断类型 T 是否可以隐式转换为类型 U。它的作用是进行编译时（constexpr）判断, 只要 Callback 可以转换 为这四种
    // std::function 类型之一，编译就不会报错
    static_assert(std::is_convertible_v<Callback, RawCallback> ||
                      std::is_convertible_v<Callback, RawCallbackWithInfo> ||
                      std::is_convertible_v<Callback, NodeCallback> ||
                      std::is_convertible_v<Callback, NodeCallbackWithInfo>,
                  "callback type error");

    IpcSubscriber(int32_t const domain, std::string const& topic, Callback&& callback, std::string const& qos_id = {})
        : domain_id_(domain), topic_name_(topic) {
        UNUSED(qos_id);
        type::TypeTraits const topic_type = type::ExtractTraits(Message{});

        auto topic_callback = [this, callback = std::move(callback)](BufferPtr const& buffer,
                                                                     MessageInfo const* info) -> void {
            // make compiler happy for without info callbacks(because info may be not used).
            UNUSED(info);

            // buffer to message
            static type::Serializer<Message> serializer;
            auto msg_ptr = std::make_shared<Message>();
            if (!serializer.FromBuffer(*buffer, *msg_ptr)) {
                log::FatalFmt(kLogTag, "subscriber deserialize message failed: domain_id: {}, topic: {}", domain_id_,
                              topic_name_);
                return;
            }

            // determine callback type.
            if constexpr (std::is_same_v<Callback, RawCallback>) {
                // 执行回调
                callback(*msg_ptr);
            } else if constexpr (std::is_same_v<Callback, RawCallbackWithInfo>) {
                // 执行回调
                callback(*msg_ptr, info);
            } else if constexpr (std::is_same_v<Callback, NodeCallback>) {
                // 执行回调
                callback(msg_ptr);
            } else if constexpr (std::is_same_v<Callback, NodeCallbackWithInfo>) {
                // 执行回调
                callback(msg_ptr, info);
            }

            typeless_ =
                std::make_unique<IpcSubscriberTypeless>(domain_id_, topic_name_, topic_type, std::move(topic_callback));
        };
    }
    virtual ~IpcSubscriber() = default;
    DISABLE_COPY_AND_MOVE(Subscriber)

    void Subscribe() {
        if (typeless_ == nullptr) {
            typeless_->Subscribe();
        }
    }

    void Unsubscribe() {
        if (typeless_ != nullptr) {
            typeless_->Unsubscribe();
        }
    }

private:
    int32_t domain_id_{0};
    std::string topic_name_{};
    std::unique_ptr<IpcSubscriberTypeless> typeless_{nullptr};
};

}  // namespace ws::ipc