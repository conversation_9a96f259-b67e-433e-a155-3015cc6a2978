#ifndef SUBSCRIBER_HPP_
#define SUBSCRIBER_HPP_

#include <functional>
#include <memory>

#include "utils/macros.hpp"

namespace ws::ipc {

static constexpr int32_t kAlignedBytes32{32};
struct MessageInfo final {
    /**
     * \brief  Timestamp(ns) when the message was being sent by source.
     *
     */
    int64_t source_timestamp;

    /**
     * \brief  Timestamp(ns) when the message was received.
     *
     */
    int64_t recept_timestamp;

    /**
     * \brief  The publication sequence number of the message.
     *
     */
    int64_t source_seqnum;

    /**
     * \brief  The reception sequence number when sample was committed by a data reader.
     *
     */
    int64_t recept_seqnum;
} __attribute__((aligned(kAlignedBytes32)));

template <typename Message, typename Callback = std::function<void(Message const&)>>
class Subscriber {
public:
    /**
     * \brief Destroy the Subscriber object
     *
     */
    virtual ~Subscriber() = default;
    DISABLE_COPY_AND_MOVE(Subscriber)

    /**
     * \brief  pure virtual function to be override by concrete subscriber to subscribe a message.
     *
     */
    virtual void Subscribe() = 0;

    /**
     * \brief  pure virtual function to be override by concrete subscriber to unsubscribe a message.
     *
     */
    virtual void Unsubscribe() = 0;

private:
    /**
     * \brief  Constructor a new Subscriber object.
     *
     */
    Subscriber() = default;
};

template <typename Message, typename Callback = std::function<void(Message const&)>>
using SubscriberPtr = std::unique_ptr<Subscriber<Message, Callback>>;

}  // namespace ws::ipc

#endif  // SUBSCRIBER_HPP_