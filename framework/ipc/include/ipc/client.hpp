#ifndef CLIENT_HPP_
#define CLIENT_HPP_

#include <chrono>
#include <functional>
#include <memory>

#include "utils/macros.hpp"
namespace ws::ipc {

enum class RequestStatus : uint8_t {
    kSendAndReceiveSuccess = 1U,  // 发送请求和接收响应成功
    kSendRequestFail = 2U,        // 发送请求失败
    kReceiveResponseFail = 3U     // 接收响应失败
};

template <typename RequestType, typename ResponseType>
class Client {
public:
    virtual ~Client() = default;
    DISABLE_COPY_AND_MOVE(Client)

    /**
     * \brief  Alias of the callback function.
     *
     */
    using OnResponseCallback = std::function<void(RequestStatus const&, ResponseType const&)>;

    /**
     * \brief  Send request to Server and wait for the response asynchronously.
     *
     * \param [In] request      Request to be sent to Server.
     * \param [In] callback     The callback function for response handling.
     * \param [In] expiry       The timeout in nanoseconds which the Client wait for the returned response from Server.
     */
    virtual void AsyncRequest(RequestType&& request, OnResponseCallback&& callback,
                              std::chrono::nanoseconds&& expiry = std::chrono::nanoseconds::zero()) = 0;
    /**
     * \brief   Send request to Server and wait for the response synchronously.
     *
     * \param [In] request      Request to be sent to Server.
     * \param [Out] response    The response data of request.
     * \param [In] expiry       The timeout in nanoseconds which the Client wait for the returned response from Server.
     * @return RequestStatus    The returned request status.
     */
    virtual RequestStatus SyncRequest(RequestType const& request, ResponseType& response,
                                      std::chrono::nanoseconds&& expiry = std::chrono::nanoseconds::zero()) = 0;

private:
    /**
     * \brief Construct a new Client object
     *
     */
    Client() = default;
};

template <typename RequestType, typename ResponseType>
using ClientPtr = std::unique_ptr<Client<RequestType, ResponseType>>;

}  // namespace ws::ipc

#endif  // CLIENT_HPP_
