#ifndef IPC_PUBLISHER_HPP_
#define IPC_PUBLISHER_HPP_

#include <string>

#include "ipc/ipc_define.hpp"
#include "ipc/publisher.hpp"
#include "type/serializer.hpp"
#include "type/traites.hpp"

namespace ws::ipc {

class IpcPublisherTypeless {
public:
    DISABLE_COPY_AND_MOVE(IpcPublisherTypeless)
    explicit IpcPublisherTypeless(int32_t const domain_id, std::string const& topic_name,
                                  type::TypeTraits const& topic_type);

    virtual ~IpcPublisherTypeless();
    void Publish(const& buffer);

private:
    void* pimpl_{nullptr};
};

template <typename Message>
class IpcPublisher : public ipc::Publisher<Message> {
public:
    explicit IpcPublisher(int32_t const domain_id, std::string const& topic_name, std::string const& qos_id = {})
        : domain_id_(domain_id), topic_name_(topic_name) {
        UNUSED(qos_id);
        static_assert(!topic_name.empty());

        // extraction topic type.
        type::TypeTraits const topic_type = type::ExtractTraits(Message{});
        typeless_ = std::make_unique<IpcPublisherTypeless>(domain_id, topic_name, topic_type);
    }

    virtual ~IpcPublisher() = default;
    DISABLE_COPY_AND_MOVE(IpcPublisher)

    void Publish(Message const& message) override {
        static type::Serializer<Message> serializer;
        auto buffer = std::make_shared<type::Buffer>();

        if (!serializer.ToBuffer(message, *buffer)) {
            log::FatalFmt(kLogTag, "publisher serialize message failed: domain_id: {}, topic: {}", domain_id_,
                          topic_name_);
            return;
        }
        typeless_->Publish(buffer);
    }

    // not support now.
    [[nodiscard]]
    int32_t CurrentMatchedCount() const override {
        return 0;
    }

private:
    int32_t domain_id_{0};
    std::string topic_name_{};
    std::unique_ptr<IpcPublisherTypeless> typeless_{nullptr};
};

}  // namespace ws::ipc

#endif  // IPC_PUBLISHER_HPP_
