#ifndef PUBLISHER_HPP_
#define PUBLISHER_HPP_

#include <memory>

#include "utils/macros.hpp"

namespace ws::ipc {

template <typename Message>
class Publisher {
public:
    virtual ~Publisher() = default;
    DISABLE_COPY_AND_MOVE(Publisher)

    /**
     * \brief  pure virtual function to be override by concrete publisher to publish a message.
     *
     * \param [dir] msg
     */
    virtual void Publish(Message const& msg) const = 0;

    /**
     * \brief  Get the current matched subscriber currently.
     *
     * @return int32_t
     */
    [[nodiscard]]
    virtual int32_t CurrentMatchedCount() const = 0;

private:
    /**
     * \brief  Constructor a new Publisher object.
     *
     */
    Publish() = default;
};

template <typename Message>
using PublisherPtr = std::unique_ptr<Publisher<Message>>;

}  // namespace ws::ipc

#endif  // PUBLISHER_HPP_