#ifndef IPC_SERVER_HPP_
#define IPC_SERVER_HPP_

#include <cassert>

#include "ipc/ipc_define.hpp"
#include "ipc/server.hpp"
#include "type/serializer.hpp"

namespace ws::ipc {

class IpcServerTypeless {
public:
    // server端需要声明request的callback.
    using IpcRequestCallback = std::function<int32_t(BufferPtr const&, BufferPtr&)>;

    explicit IpcServerTypeless(int32_t const domain_id, std::string const& service_id);
    virtual ~IpcServerTypeless() = default;
    DISABLE_COPY_AND_MOVE(IpcServerTypeless)

    void AsyncResponse(IpcRequestCallback&& callback);
    void StopReceiveRequests();

private:
    // mest be void* as template class member.
    void* pimpl_{nullptr};
};

/**
 * \brief  The Server class which waits for Client requests and handles the requests.
 *
 * \tparam RequestType  The type of request message.
 * \tparam ResponseType The type of response message.
 */
template <typename RequestType, typename ResponseType>
class IpcServer : public ipc::Server<RequestType, ResponseType> {
public:
    using OnRequestCallback = std::function<int32_t(ServiceStatus const&, RequestType const&, ResponseType&)>;

    explicit IpcServer(int32_t const domain_id, std::string const& service_id, std::string const& qos_id = {})
        : domain_id_(domain_id), service_id_(service_id) {
        UNUSED(qos_id);
        static_assert(!service_id.empty());
        typeless_ = std::make_unique<IpcServerTypeless>(domain_id, service_id);
    }

    virtual ~IpcServer() = default;
    DISABLE_COPY_AND_MOVE(IpcServer)

    /**
     * \brief  Start waiting for the request from Client.
     *
     * \param [dir] callback The Callback function for request handling.
     */
    void AsyncResponses(OnRequestCallback&& callback) {
        auto request_callback = [this, callback = std::move(callback)](BufferPtr const& request_buffer,
                                                                       BufferPtr& response_buffer) -> int32_t {
            // 接收到的request_buffer需要反序列化到request_serializer里
            static type::Serializer<RequestType> request_serializer;
            RequestType request{};
            if (!request_serializer.FromBuffer(*request_buffer, request)) {
                log::FatalFmt(kLogTag, "server deserialize request failed: domain_id: {}, service_id: {}", domain_id_,
                              service_id_);
                return kUserRequestFail;
            }

            // run user request callback.
            ResponseType response{};
            int32_t status = callback(ServiceStatus::kHandleRequestSuccess, request, response);

            // response to buffer.
            static type::Serializer<ResponseType> response_serializer;
            if (!response_serializer.ToBuffer(response, response_buffer)) {
                log::FatalFmt(kLogTag, "server serialize response failed: domain_id: {}, service_id: {}", domain_id_,
                              service_id_);
                return kUserResponseFail;
            }

            return status;
        };

        typeless_->AsyncResponse(std::move(request_callback));
    }

    /**
     * \brief  Stop waiting for and taking the requests from Client.
     *
     */
    void StopReceiveRequests() {
        if (typeless_) {
            typeless_->StopReceiveRequests();
        }
    }

private:
    int32_t domain_id_{0};
    std::string service_id_{};
    std::unique_ptr<IpcServerTypeless> typeless_{nullptr};
};

}  // namespace ws::ipc
