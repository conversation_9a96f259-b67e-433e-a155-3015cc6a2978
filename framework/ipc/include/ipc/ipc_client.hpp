#ifndef IPC_CLIENT_HPP_
#define IPC_CLIENT_HPP_

#include <cassert>

#include "ipc/client.hpp"
#include "ipc/ipc_define.hpp"
#include "type/serializer.hpp"

namespace ws::ipc {

class IpcClientTypeless {
public:
    using IpcResponseCallback = std::function<void(RequestStatus const& status, BufferPtr const& response_buffer)>;

    explicit IpcClientTypeless(int32_t const domain_id, std::string const& client_id, std::string const& service_id);
    virtual ~IpcClientTypeless();

    void AsyncRequest(BufferPtr const& request_buffer, IpcResponseCallback const& callback,
                      std::chrono::nanoseconds const& expiry_ns);

    RequestStatus SyncRequest(BufferPtr const& request_buffer, BufferPtr& response_buffer,
                              std::chrono::nanoseconds const& expiry_ns);

private:
    void* pimpl_{nullptr};
};

/**
 * \brief  The Client class with requester which send requests to Server and waits for the response from Server.
 *
 * \tparam RequestType  Type of request message.
 * \tparam ResponseType Type of response message.
 */
template <typename RequestType, typename ResponseType>
class IpcClient : public ipc::Client<RequestType, ResponseType> {
public:
    using OnResponseCallback = std::function<void(RequestStatus const&, ResponseType const&)>;

    explicit IpcClient(int32_t const domain_id, std::string const& client_id, std::string const& service_id,
                       std::string const& qos_id = {})
        : domian_id_(domain_id), client_id_(client_id), service_id_(service_id) {
        UNUSED(qos_id);
        static_assert(!client_id.empty());
        static_assert(!service_id.empty());
        typeless_ = std::make_unique<IpcClientTypeless>(domain_id, client_id, service_id);
    }

    virtual ~IpcClient() = default;
    DISABLE_COPY_AND_MOVE(IpcClient)

    /**
     * \brief  Send request to Server and wait for the response asynchronously.
     *
     * \param [In] request     Request to be sent to Server.
     * \param [In] callback    The callback function for response handling.
     * \param [In] expiry      The timeout in nanoseconds which the Client wait for the returned response from Server.
     */
    void AsyncRequest(RequestType&& request, OnResponseCallback&& callback,
                      std::chrono::nanoseconds&& expiry_ns = std::chrono::nanoseconds::zero()) override {
        // request需要序列化
        static type::Serializer<RequestType> request_serializer{};
        auto request_buffer = std::make_shared<type::Buffer>();
        // 把request序列化到request_buffer里
        if (!request_serializer.ToBuffer(std::move(request), *request_buffer)) {
            callback(RequestStatus::kSendRequestFail, {});
            log::FatalFmt(kLogTag, "client serialize request failed: domain_id: {}, client_id: {}, service_id: {}",
                          domian_id_, client_id_, service_id_);
            return;
        }

        // 捕获列表这里是否应该写std::forward<OnResponseCallback>(callback)?
        auto response_callback = [this, callback = std::move(callback)](RequestStatus const& status,
                                                                        BufferPtr const& response_buffer) -> void {
            static type::Serializer<ResponseType> response_serializer;
            ResponseType response{};
            // 将接收到的response_buffer反序列化到response里
            if (!response_serializer.FromBuffer(*response_buffer, response)) {
                callback(RequestStatus::kReceiveResponseFail, {});
                log::FatalFmt(kLogTag,
                              "client deserialize response failed: domain_id: {}, client_id: {}, "
                              "service_id: {}",
                              domian_id_, client_id_, service_id_);
                return;
            }

            // run user response callback.
            callback(status, response);
        };

        typeless_->AsyncRequest(request_buffer, std::move(response_callback), expiry_ns);
    }

    /**
     * \brief  Send request to Server and wait for the response synchronously.
     *
     * \param [In] request      Request to be sent to Server.
     * \param [Out] response    The response data of request.
     * \param [In] expiry_ns    The timeout in nanoseconds which the Client wait for the returned response from Server.
     * @return RequestStatus    The returned request status.
     */
    RequestStatus SyncRequest(RequestType const& request, ResponseType& response,
                              std::chrono::nanoseconds&& expiry_ns = std::chrono::nanoseconds::zero()) override {
        // request需要序列化
        static type::Serializer<RequestType> request_serializer{};
        auto request_buffer = std::make_shared<type::Buffer>();
        // 把request序列化到request_buffer里
        if (!request_serializer.ToBuffer(request, *request_buffer)) {
            log::FatalFmt(kLogTag, "client serialize request failed: domain_id: {}, client_id: {}, service_id: {}",
                          domian_id_, client_id_, service_id_);
            return RequestStatus::kSendRequestFail;
        }

        // call user request callback.
        BufferPtr response_buffer = std::make_shared<type::Buffer>();
        RequestStatus status = typeless_->SyncRequest(request_buffer, response_buffer, expiry_ns);

        if (status != RequestStatus::kSendAndReceiveSuccess) {
            static type::Serializer<ResponseType> response_serializer;
            // 将接收到的response_buffer反序列化到函数传入的response里
            if (!response_serializer.FromBuffer(*response_buffer, response)) {
                log::FatalFmt(kLogTag,
                              "client deserialize response failed: domain_id: {}, client_id: {}, "
                              "service_id: {}",
                              domian_id_, client_id_, service_id_);
                return RequestStatus::kReceiveResponseFail;
            }
        }

        return status;
    }

private:
    int32_t domian_id_{0};
    std::string client_id_{};
    std::string service_id_{};
    std::unique_ptr<IpcClientTypeless> typeless_{nullptr};
};

}  // namespace ws::ipc
