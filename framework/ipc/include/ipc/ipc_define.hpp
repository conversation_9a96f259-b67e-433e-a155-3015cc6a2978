#ifndef IPC_DEFINE_HPP_
#define IPC_DEFINE_HPP_

#include "ipc/client.hpp"
#include "ipc/server.hpp"
#include "ipc/subscriber.hpp"
#include "log/log.hpp"
#include "type/serializable.hpp"
#include "utils/macros.hpp"

namespace ws::ipc {

using MessageInfo = ws::ipc::MessageInfo;
using RequestStatus = ws::ipc::RequestStatus;
using ServiceStatus = ws::ipc::RequestStatus;
using BufferPtr = std::shared_ptr<ws::type::Buffer>;

static constexpr char const* kLogTag{"ipc"};
static constexpr int32_t kUserRequestSucc = 0;
static constexpr int32_t kUserRequestFail = -1;
static constexpr int64_t kRequestTimeoutMaxNs = 3'000'000'000L;  // 3s

}  // namespace ws::ipc
