#ifndef SERVER_HPP_
#define SERVER_HPP_

#include <functional>
#include <memory>

#include "utils/macros.hpp"

namespace ws::ipc {

enum class ServiceStatus : uint8_t { kHandleRequestSuccess = 1U, kHandleRequestFail = 2U };

template <typename RequestType, typename ResponseType>
class Server {
public:
    /**
     * \brief Destroy the Server object.
     *
     */
    virtual ~Server() = default;
    DISABLE_COPY_AND_MOVE(Server)

    /**
     * \brief  user callback with return result, 0 is success, -1 is fail.
     *
     */
    using OnRequestCallback = std::function<void(ServerStatus const&, RequestType const&, ResponseType&)>;

    /**
     * \brief  Start waiting for the request from Client.
     *
     * \param [In] callback The Callback function for request handling.
     */
    virtual void AsyncResponses(OnRequestCallback&& callback) = 0;

    /**
     * \brief  Stop waiting for and taking the requests from Client.
     *
     */
    virtual void StopReceiveRequests() = 0;

private:
    /**
     * \brief Construct a new Server object.
     *
     */
    Server() = default;
};

template <typename RequestType, typename ResponseType>
using ServerPtr = std::unique_ptr<Server<RequestType, ResponseType>>;

}  // namespace ws::ipc

#endif  // SERVER_HPP_
