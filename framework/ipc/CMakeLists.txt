project(ipc)


file(GLOB ${PROJECT_NAME}_SRC_FILES src/*.cpp)

add_library(${PROJECT_NAME} 
    SHARED 
        ${${PROJECT_NAME}_SRC_FILES}
)
target_include_module_directories(${PROJECT_NAME}
    PUBLIC
        log
        type
        utils
)
target_include_directories(${PROJECT_NAME} 
    PUBLIC 
        include
        private
)
target_link_libraries(${PROJECT_NAME} 
    PUBLIC 
        pthread 
)
