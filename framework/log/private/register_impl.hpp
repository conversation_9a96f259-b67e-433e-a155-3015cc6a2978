#ifndef LOG_REGISTER_IMPL_HPP_
#define LOG_REGISTER_IMPL_HPP_

#include <mutex>

// #include "log/formatter.hpp"
#include "details/periodic_flusher.hpp"
#include "log/logger.hpp"
#include "log/register.hpp"
#include "utils/macros.hpp"

namespace ws::log {

enum class SinkMode { kSinkToAndroid, kSinkToConsole, kSinkToSingleFile, kSinkToNull };

enum class LogMode { kSync, kAsync };

namespace details {
class AsyncThreadPool;
class PeriodicFlusher;
}  // namespace details

class Register::RegisterImpl {
public:
    RegisterImpl();
    ~RegisterImpl() = default;
    DISABLE_COPY_AND_MOVE(RegisterImpl)

    Logger* GetDefaultRaw();

#ifdef BUILD_TESTING
    void SetLogLevel(level::LogLevel const lvl);
    void SetDefaultLogger(std::shared_ptr<Logger> new_default_logger);
#endif  // BUILD_TESTING

private:
    void LoadLogEnvCfg();

    static LogMode GetLogModeFromEnv();
    static SinkMode GetWriteModeFromEnv();

    [[nodiscard]]
    static bool IsEnabled(std::string const& env_name, bool default_value);

    [[nodiscard]]
    static std::size_t GetAsyncQueueSlotFromEnv();
    [[nodiscard]]
    static std::size_t GetLogFileSizeFromEnv();
    [[nodiscard]]
    static bool AsyncQueueSlotValidityCheck(std::size_t const async_queue_slot);
    [[nodiscard]]
    static bool LogFileSizeValidityCheck(std::size_t const log_file_size);

    void CreateDefaultLogger(LogMode const log_mode, SinkMode const sink_mode, std::size_t const async_queue_slot,
                             std::size_t const log_file_size);

    [[nodiscard]]
    static std::string GetProcessNameFromProc(pid_t const pid);

    void PeriodicFlush();

    std::shared_ptr<details::AsyncThreadPool> thread_pool_;
    std::unique_ptr<details::PeriodicFlusher> periodic_flusher_;
    std::shared_ptr<Logger> default_logger_;

#ifdef BUILD_TESTING
    std::mutex mt_;
#endif  // BUILD_TESTING
};

}  // namespace ws::log

#endif  // LOG_REGISTER_IMPL_HPP_