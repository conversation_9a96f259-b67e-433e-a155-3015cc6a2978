#ifndef LOG_ASYNC_LOG_MSG_HPP_
#define LOG_ASYNC_LOG_MSG_HPP_

#include "async_msg_type.hpp"
#include "details/log_msg_buffer.hpp"

namespace ws::log {
class AsyncLogger;
}  // namespace ws::log

namespace ws::log::details {

class AsyncLogMsg : public LogMsgBuffer {
public:
    AsyncLogMsg() = default;

    explicit AsyncLogMsg(std::shared_ptr<AsyncLogger>&& worker, AsyncMsgType msg_type, LogMsg const& log_msg);

    explicit AsyncLogMsg(std::shared_ptr<AsyncLogger>&& worker, AsyncMsgType msg_type);

    explicit AsyncLogMsg(AsyncMsgType msg_type);

    ~AsyncLogMsg() = default;

    DISABLE_COPY(AsyncLogMsg)

    AsyncMsgType msg_type_{AsyncMsgType::kLog};
    std::shared_ptr<AsyncLogger> worker_ptr_{};
};

}  // namespace ws::log::details

#endif  // LOG_ASYNC_LOG_MSG_HPP_
