#ifndef LOG_LOG_MSG_HPP_
#define LOG_LOG_MSG_HPP_

#include <chrono>
#include <string_view>

#include "log/log_level.hpp"
#include "utils/macros.hpp"
namespace ws::log::details {

class LogMsg {
public:
    LogMsg() = default;

    explicit LogMsg(level::LogLevel const lvl, std::string_view tag, std::string_view msg);

    ~LogMsg() = default;

    DISABLE_MOVE(LogMsg)

    static std::size_t GetPid() noexcept;
    static std::size_t GetThreadId() noexcept;

    std::chrono::system_clock::time_point time_;
    level::LogLevel level_{};
    std::string_view tag_;
    std::string_view msg_;
    std::size_t pid_{0};
    std::size_t thread_id_{0};
};

}  // namespace ws::log::details

#endif  // LOG_LOG_MSG_HPP_