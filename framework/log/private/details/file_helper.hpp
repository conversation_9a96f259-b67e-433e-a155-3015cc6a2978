#ifndef LOG_FILE_HELPER_HPP_
#define LOG_FILE_HELPER_HPP_

#include <cstdio>  // std::FILE
#include <string>

#include "fmt/format.h"  // fmt::basic_memory_buffer
#include "fmt_defines.hpp"
#include "utils/macros.hpp"

namespace ws::log::details {
class FileHelper {
public:
    FileHelper() = default;
    explicit FileHelper(std::string file_path);
    ~FileHelper();
    DISABLE_COPY_AND_MOVE(FileHelper)

    void Open();
    void Close();
    void Flush();
    void Write(FmtBuffer const& buf);
    [[nodiscard]] std::size_t Size() const;
    [[nodiscard]] std::string const& FileName() const;

private:
    static std::string DirName(std::string const& file_path);
    static bool CreateDir(std::string const& dir);

    std::FILE* target_file_{nullptr};
    std::string file_path_{};
};

}  // namespace ws::log::details

#endif  // LOG_FILE_HELPER_HPP_