#ifndef LOG_CIRCULAR_BLOCKING_QUEUE_HPP_
#define LOG_CIRCULAR_BLOCKING_QUEUE_HPP_

#include <condition_variable>
#include <mutex>

#include "circular_queue.hpp"
#include "utils/macros.hpp"

namespace ws::log::details {

template <typename T>
class CircularBlockingQueue {
public:
    explicit CircularBlockingQueue(std::size_t const capacity) : rb_{capacity} {}
    ~CircularBlockingQueue() = default;
    DISABLE_COPY_AND_MOVE(CircularBlockingQueue)

    // try to enqueue and block if no space left.
    void Enqueue(T&& item);

    // enqueue immediately, overrun oldest message in the queue if no space left.
    void EnqueueNoWait(T&& item);

    void Dequeue(T& item);

    bool TryDequeue(T& item);

    std::size_t Size() const {
        std::lock_guard<std::mutex> lock{queue_mt_};
        return rb_.size();
    }

private:
    mutable std::mutex queue_mt_{};
    std::condition_variable push_cv_{};
    std::condition_variable pop_cv_{};
    CircularQueue<T> rb_;
};

template <typename T>
void CircularBlockingQueue<T>::Enqueue(T&& item) {
    {
        std::unique_lock<std::mutex> lock{queue_mt_};
        pop_cv_.wait(lock, [this]() { return !this->rb_.full(); });

        rb_.push_back(std::move(item));
    }

    push_cv_.notify_one();
}

template <typename T>
void CircularBlockingQueue<T>::EnqueueNoWait(T&& item) {
    {
        std::unique_lock<std::mutex> const lock{queue_mt_};
        rb_.push_back(std::move(item));
    }

    push_cv_.notify_one();
}

template <typename T>
void CircularBlockingQueue<T>::Dequeue(T& item) {
    {
        std::unique_lock<std::mutex> lock{queue_mt_};
        push_cv_.wait(lock, [this]() { return !this->rb_.empty(); });

        item = std::move(rb_.front());
        rb_.pop_front();
    }

    pop_cv_.notify_one();
}

template <typename T>
bool CircularBlockingQueue<T>::TryDequeue(T& item) {
    {
        std::unique_lock<std::mutex> const lock{queue_mt_};
        if (rb_.empty()) {
            return false;
        }

        item = std::move(rb_.front());
        rb_.pop_front();
    }

    pop_cv_.notify_one();
    return true;
}

}  // namespace ws::log::details

#endif  // LOG_CIRCULAR_BLOCKING_QUEUE_HPP_
