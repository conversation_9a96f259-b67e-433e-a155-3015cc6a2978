#ifndef LOG_LOG_MSG_BUFFER_HPP_
#define LOG_LOG_MSG_BUFFER_HPP_

#include "details/log_msg.hpp"
#include "fmt_defines.hpp"
#include "utils/macros.hpp"

namespace ws::log::details {
class LogMsgBuffer : public LogMsg {
public:
    LogMsgBuffer() = default;
    explicit LogMsgBuffer(LogMsg const& orig_msg);
    ~LogMsgBuffer() = default;

    LogMsgBuffer(LogMsgBuffer const& other) = delete;
    LogMsgBuffer& operator=(LogMsgBuffer const& other) = delete;

    LogMsgBuffer(LogMsgBuffer&& other) noexcept;
    LogMsgBuffer& operator=(LogMsgBuffer&& other) noexcept;

private:
    void UpdateStringViews();

    FmtBuffer buffer_{};
};

}  // namespace ws::log::details

#endif  // LOG_LOG_MSG_BUFFER_HPP_
