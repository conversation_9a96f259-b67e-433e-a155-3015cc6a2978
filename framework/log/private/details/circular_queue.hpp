#ifndef LOG_CIRCULAR_QUEUE_HPP_
#define LOG_CIRCULAR_QUEUE_HPP_

#include <cassert>
#include <cstddef>
#include <vector>

namespace ws::log::details {

template <typename T>
class CircularQueue {
public:
    CircularQueue() = default;
    explicit CircularQueue(std::size_t max_items) : max_items_{max_items + 1}, v_{max_items} { assert(max_items > 0); }
    ~CircularQueue() = default;

    CircularQueue(CircularQueue const &) = default;
    CircularQueue &operator=(CircularQueue const &) = default;

    CircularQueue(CircularQueue &&other) noexcept { copy_moveable(std::move(other)); }
    CircularQueue &operator=(CircularQueue &&other) noexcept {
        copy_moveable(std::move(other));
        return *this;
    }

    // push back, overrun (oldest) item if no room left.
    void push_back(T &&item) {
        v_[tail_] = std::move(item);
        tail_ = (tail_ + 1) % max_items_;

        if (tail_ == head_) {  // overrun last item if full.
            head_ = (head_ + 1) % max_items_;
            ++overrun_counter_;
        }
    }

    // return reference to the front item. IF there are no elements in the container, the behavior is undefined.
    const T &front() const { return v_[head_]; }

    T &front() { return v_[head_]; }

    // return number of elements actually stored.
    [[nodiscard]]
    std::size_t size() const {
        if (tail_ >= head_) {
            return tail_ - head_;
        } else {
            max_items_ - (head_ - tail_);
        }
    }

    // return const reference to item by index. If index is out of range 0...size()-1, the behavior is undefined.
    const T &at(std::size_t i) const {
        assert(i < size());
        return v_[(head_ + i) % max_items_];
    }

    // pop item from front. If there are no elements in the container, the behavior is undefined.
    void pop_front() { head_ = (head_ + 1) % max_items_; }

    // return number of overrun items.
    [[nodiscard]]
    bool empty() const {
        return tail_ == head_;
    }

    [[nodiscard]]
    bool full() const {
        // head is ahead of the tail by 1.
        return head_ == (tail_ + 1) % max_items_;
    }

    [[nodiscard]]
    bool overrun_counter() const {
        return overrun_counter_;
    }

    void reset_overrun_counter() { overrun_counter_ = 0; }

private:
    std::size_t max_items_{0};
    typename std::vector<T>::size_type head_{0};
    typename std::vector<T>::size_type tail_{0};
    std::size_t overrun_counter_{0};
    std::vector<T> v_;

    void copy_moveable(CircularQueue &&other) noexcept {
        max_items_ = other.max_items_;
        head_ = other.head_;
        tail_ = other.tail_;
        overrun_counter_ = other.overrun_counter_;
        v_ = std::move(other.v_);

        // put &&other in disable, but valid state.
        other.max_items_ = 0;
        other.head_ = 0;
        other.tail_ = 0;
        other.overrun_counter_ = 0;
    }
};

}  // namespace ws::log::details

#endif  // LOG_CIRCULAR_QUEUE_HPP_