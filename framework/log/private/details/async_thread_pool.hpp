#ifndef LOG_ASYNC_THREAD_POOL_HPP_
#define LOG_ASYNC_THREAD_POOL_HPP_

#include <thread>
#include <vector>

#include "async_overflow_policy.hpp"
#include "details/async_log_msg.hpp"
#include "details/circular_blocking_queue.hpp"
#include "utils/macros.hpp"

namespace ws::log {
class AsyncLogger;
}  // namespace ws::log

namespace ws::log::details {
class AsyncThreadPool {
public:
    using AsyncLoggerPtr = std::shared_ptr<AsyncLogger>;
    using QueueType = CircularBlockingQueue<AsyncLogMsg>;

    explicit AsyncThreadPool(std::size_t const worker_count, std::size_t const queue_size);
    ~AsyncThreadPool();
    DISABLE_COPY_AND_MOVE(AsyncThreadPool)

    void PostLog(AsyncLoggerPtr&& work_ptr, details::LogMsg const& log_msg, AsyncOverflowPolicy overflow_policy);
    void PostFlush(AsyncLoggerPtr&& work_ptr, AsyncOverflowPolicy overflow_policy);
    void PostAsyncMsg(AsyncLogMsg&& async_log_msg, AsyncOverflowPolicy overflow_policy);

private:
    void WorkerLoop();
    bool FetchLogMsg();

    QueueType q_;
    std::vector<std::thread> threads_;
};

}  // namespace ws::log::details

#endif  // LOG_ASYNC_THREAD_POOL_HPP_
