#ifndef LOG_FMT_HELPER_HPP_
#define LOG_FMT_HELPER_HPP_

#include <chrono>

#include "fmt/core.h"
#include "fmt_defines.hpp"

namespace ws::log::details::fmt_helper {
inline void AppendStringView(std::string_view view, FmtBuffer& dest) {
    char const* buf_ptr = view.data();
    dest.append(buf_ptr, buf_ptr + view.size());
}

template <typename ToDuration>
inline ToDuration TimeFraction(std::chrono::system_clock::time_point tp) {
    auto duration = tp.time_since_epoch();
    auto secs = std::chrono::duration_cast<std::chrono::seconds>(duration);
    return std::chrono::duration_cast<ToDuration>(duration) - std::chrono::duration_cast<ToDuration>(secs);
}

}  // namespace ws::log::details::fmt_helper

#endif  // LOG_FMT_HELPER_HPP_
