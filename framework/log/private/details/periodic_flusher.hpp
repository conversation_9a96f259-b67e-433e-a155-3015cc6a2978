#ifndef LOG_PERIODIC_FLUSHER_HPP_
#define LOG_PERIODIC_FLUSHER_HPP_

#include <chrono>
#include <condition_variable>
#include <functional>
#include <thread>

#include "utils/macros.hpp"

namespace ws::log::details {

class PeriodicFlusher {
public:
    PeriodicFlusher(std::function<void()> const& periodic_flush);
    ~PeriodicFlusher();

    DISABLE_COPY_AND_MOVE(PeriodicFlusher)
private:
    bool active_{true};
    std::mutex mtx_;
    std::condition_variable cv_;
    std::thread worker_;
};

}  // namespace ws::log::details

#endif  // LOG_PERIODIC_FLUSHER_HPP_