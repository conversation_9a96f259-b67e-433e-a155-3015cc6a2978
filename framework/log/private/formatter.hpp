#ifndef LOG_FORMATTER_HPP_
#define LOG_FORMATTER_HPP_

#include <chrono>
#include <ctime>  // std::time_t / std::tm
#include <string>
#include <vector>

#include "details/log_msg.hpp"
#include "fmt_defines.hpp"
#include "utils/macros.hpp"

namespace ws::log {
enum class TimeZoneType {
    kLocal,  // log localtime
    kUtc
};

class Formatter {
    static constexpr auto const kDefaultEol{'\n'};

    static constexpr int32_t kDateTimeCacheSize{30};

    using FmtDateTimeCacheBuffer = fmt::basic_memory_buffer<char, kDateTimeCacheSize>;

public:
    explicit Formatter(TimeZoneType time_zone_type = TimeZoneType::kLocal);
    ~Formatter() = default;
    DISABLE_COPY_AND_MOVE(Formatter)

    // 格式化函数 格式固定为: 月-日 时:分:秒.毫秒 pid tid tag msg
    void Format(details::LogMsg const& log_msg, FmtBuffer& dest);

private:
    void FormatPad(int const n);
    static void FormatMillis(int n, FmtBuffer& dest);

    std::tm GetTime(details::LogMsg const& log_msg);
    static std::tm LocalTime(std::time_t const& time_tt);
    static std::tm GmTime(std::time_t const& time_tt);

    TimeZoneType time_zone_type_;
    std::chrono::seconds last_log_secs_;
    std::tm cache_tm_{};
    FmtDateTimeCacheBuffer datetime_cache_;
};

}  // namespace ws::log

#endif  // LOG_FORMATTER_HPP_