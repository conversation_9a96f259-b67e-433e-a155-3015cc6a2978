#ifndef LOG_SINKS_ANDROID_SINK_HPP_
#define LOG_SINKS_ANDROID_SINK_HPP_
///////// 使用此模式可执行文件必须要使用sudo命令执行
#include <iostream>

#include "android/log.h"
#include "details/log_msg.hpp"
#include "sinks/sink.hpp"

namespace ws::log::sinks {

template <int LOG_ID = log_id::LOG_ID_MAIN>
class AndroidSink final : public Sink {
public:
    AndroidSink() = default;
    ~AndroidSink() override = default;
    DISABLE_COPY_AND_MOVE(AndroidSink)

    void Log(details::LogMsg const& log_msg) override {
        auto const prio = ConvertToAndroidLogPriority(log_msg.level_);

        auto tag = log_msg.tag_.data();
        auto msg = log_msg.msg_.data();

        AndroidLog(prio, tag, msg);
    }
    void Flush() override {}

private:
    static android_LogPriority ConvertToAndroidLogPriority(level::LogLevel const lvl) {
        switch (lvl) {
            case level::LogLevel::kDebug:
                return ANDROID_LOG_DEBUG;
            case level::LogLevel::kInfo:
                return ANDROID_LOG_INFO;
            case level::LogLevel::kWarn:
                return ANDROID_LOG_WARN;
            case level::LogLevel::kError:
                return ANDROID_LOG_ERROR;
            case level::LogLevel::kFatal:
                return ANDROID_LOG_FATAL;
            default:
                return ANDROID_LOG_UNKNOWN;
        }
    }

    template <int ID = LOG_ID>
    void AndroidLog(android_LogPriority prio, char const* tag, char const* msg) {
        if constexpr (ID == static_cast<int>(log_id::LOG_ID_MAIN)) {
            __android_log_write(prio, tag, msg);
        } else {  // 备用，打到system/radio/event中
            __android_log_buf_write(ID, prio, tag, msg);
        }
    }
};

}  // namespace ws::log::sinks

#endif  // LOG_SINKS_ANDROID_SINK_HPP_
