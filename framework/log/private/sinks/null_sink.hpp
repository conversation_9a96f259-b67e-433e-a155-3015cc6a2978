#ifndef LOG_SINK_NULL_SINK_HPP_
#define LOG_SINK_NULL_SINK_HPP_

#include "sinks/sink.hpp"

namespace ws::log::sinks {
class NullSink final : public Sink {
public:
    NullSink() = default;
    ~NullSink() override = default;
    DISABLE_COPY_AND_MOVE(NullSink)

    void Log(details::LogMsg const& log_msg) override { UNUSED(log_msg); }
    void Flush() override {}
};

}  // namespace ws::log::sinks

#endif  // LOG_SINK_NULL_SINK_HPP_