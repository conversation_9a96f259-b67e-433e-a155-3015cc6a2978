#ifndef LOG_SINKS_ROTATING_FILE_SINK_HPP_
#define LOG_SINKS_ROTATING_FILE_SINK_HPP_

#include <mutex>

#include "details/file_helper.hpp"
#include "formatter.hpp"
#include "sinks/sink.hpp"

namespace ws::log::sinks {

class RotatingFileSink final : public Sink {
public:
    explicit RotatingFileSink(std::string const& base_dir, std::string const& file_name, std::size_t const max_size);
    ~RotatingFileSink() override = default;
    DISABLE_COPY_AND_MOVE(RotatingFileSink)

    void Log(details::LogMsg const& log_msg) override;
    void Flush() override;

private:
    void RotateFile();
    [[nodiscard]]
    std::string MakeLogName(std::string const& curr_time) const;

    std::string base_dir_;
    std::string log_dir_;
    std::string file_name_;
    std::string file_path_;
    std::size_t max_size_;

    std::size_t curr_size_;

    std::mutex mt_;
    details::FileHelper file_helper_;
    std::unique_ptr<Formatter> formatter_;
};

}  // namespace ws::log::sinks

#endif  // LOG_SINKS_ROTATING_FILE_SINK_HPP_
