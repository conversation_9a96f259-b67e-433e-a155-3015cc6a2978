#ifndef LOG_SINKS_STDOUT_SINK_HPP_
#define LOG_SINKS_STDOUT_SINK_HPP_

#include <array>
#include <memory>
#include <mutex>
#include <string_view>

#include "formatter.hpp"
#include "sinks/sink.hpp"

namespace ws::log::sinks {

namespace color {

static constexpr std::string_view kReset = "\x1b[0m";
static constexpr std::string_view kBlue = "\x1b[38;5;75m";
static constexpr std::string_view kGreen = "\x1b[38;5;40m";
static constexpr std::string_view kYellow = "\x1b[38;5;226m";
static constexpr std::string_view kRed = "\x1b[38;5;196m";

static constexpr std::array<std::string_view, static_cast<int>(level::LogLevel::kLevelCounter)> kColors{
    color::kBlue, color::kGreen, color::kYellow, color::kRed, color::kRed};

}  // namespace color

class StdoutSink final : public Sink {
public:
    explicit StdoutSink(FILE* target_file = stdout);
    ~StdoutSink() override = default;
    DISABLE_COPY_AND_MOVE(StdoutSink)

    void Log(details::LogMsg const& log_msg) override;
    void Flush() override;

private:
    void PrintColor(std::string_view const& color_code);

    std::mutex mt_;
    std::FILE* target_file_;
    std::unique_ptr<Formatter> formatter_;
};

}  // namespace ws::log::sinks

#endif  // LOG_SINKS_STDOUT_SINK_HPP_