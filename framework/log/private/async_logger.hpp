#ifndef LOG_ASYNC_LOGGER_HPP_
#define LOG_ASYNC_LOGGER_HPP_

#include <memory>

#include "async_overflow_policy.hpp"
#include "details/async_thread_pool.hpp"
#include "log/logger.hpp"

namespace ws::log {
class AsyncLogger : public std::enable_shared_from_this<AsyncLogger>, public Logger {
public:
    explicit AsyncLogger(std::string const& logger_name, std::shared_ptr<sinks::Sink> sink,
                         std::weak_ptr<details::AsyncThreadPool> thread_pool,
                         AsyncOverflowPolicy overflow_policy = AsyncOverflowPolicy::kOverrunOldest);

    void SinkIt(details::LogMsg const& log_msg) override;
    void BackendSinkIt(details::LogMsg const& log_msg);

    void FlushIt() override;
    void BackendFlushIt();

private:
    std::weak_ptr<details::AsyncThreadPool> thread_pool_;
    AsyncOverflowPolicy overflow_policy_;
};

}  // namespace ws::log

#endif  // LOG_ASYNC_LOGGER_HPP_
