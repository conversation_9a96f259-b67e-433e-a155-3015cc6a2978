// #include "log/log_printf.hpp"
#include "bridge.h"

#include "../../include/log/log.hpp"
#include "rust/cxx.h"

namespace ws::log::bridge {

void Debug(rust::Str tag, rust::Str fmt) {
    std::string tag_str(tag);
    std::string fmt_str(fmt);
    ws::log::Debug(tag_str.c_str(), "%s", fmt_str.c_str());
}

void Info(rust::Str tag, rust::Str fmt) {
    std::string tag_str(tag);
    std::string fmt_str(fmt);
    ws::log::Info(tag_str.c_str(), "%s", fmt_str.c_str());
}

void Warn(rust::Str tag, rust::Str fmt) {
    std::string tag_str(tag);
    std::string fmt_str(fmt);
    ws::log::Warn(tag_str.c_str(), "%s", fmt_str.c_str());
}

void Error(rust::Str tag, rust::Str fmt) {
    std::string tag_str(tag);
    std::string fmt_str(fmt);
    ws::log::Error(tag_str.c_str(), "%s", fmt_str.c_str());
}

void Fatal(rust::Str tag, rust::Str fmt) {
    std::string tag_str(tag);
    std::string fmt_str(fmt);
    ws::log::Fatal(tag_str.c_str(), "%s", fmt_str.c_str());
}

}  // namespace ws::log::bridge
