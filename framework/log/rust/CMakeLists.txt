cmake_minimum_required(VERSION 3.10)
project(ws_log_wrapper)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Find the ws_log library
find_library(WS_LOG_LIB ws_log
    PATHS 
        ${CMAKE_CURRENT_SOURCE_DIR}/target/release
        ${CMAKE_CURRENT_SOURCE_DIR}/target/debug
        /usr/local/lib
        /usr/lib
)

if(NOT WS_LOG_LIB)
    message(FATAL_ERROR "ws_log library not found. Please build it first with 'cargo build --release'")
endif()

# Add C example executable
add_executable(c_example examples/c_example.c)
target_link_libraries(c_example ${WS_LOG_LIB})

# On Unix-like systems, we need to link against the C++ standard library
if(UNIX)
    target_link_libraries(c_example stdc++)
endif()

# Installation
install(FILES include/ws_log.h DESTINATION include)
install(FILES ${WS_LOG_LIB} DESTINATION lib)
