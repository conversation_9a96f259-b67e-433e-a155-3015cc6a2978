use log::{debug, error, info, warn, LevelFilter};

#[test]
fn log_test() {
    // Initialize the logger with debug level
    ws_log::init_with_level(LevelFilter::Debug).unwrap();

    let s = String::from("hello world");
    let tag = "test";
    debug!(target: tag, "This is a debug message from Rust");
    info!(target: tag, "This is an info message from Rust");
    warn!(target: tag, "This is a warning message from Rust");
    error!(target: tag, "This is an error message from Rust");
    // There's no direct fatal in the log crate, but we can use error for critical issues
    error!(target: tag, "This is a critical/fatal message from Rust");

    // Test with formatted messages
    let value = 42;
    info!(target: tag, "The answer is {}", value);
}
