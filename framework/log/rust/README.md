# WS Log - Rust Logging Library

A Rust logging library that provides a facade over the C++ logging implementation.

## Features

- Implements the standard Rust `log` facade
- Provides C API for use from C/C++ applications
- Supports both dynamic and static linking
- Cross-compilation support

## Building the Library

### Building for the current platform

```bash
# Build in debug mode
cargo build

# Build in release mode (recommended for production)
cargo build --release
```

### Cross-compilation

To cross-compile for a different target, use the `--target` flag:

```bash
# For example, to build for ARM Linux
rustup target add aarch64-unknown-linux-gnu
cargo build --target aarch64-unknown-linux-gnu --release
```

## Usage from Rust

Add the library to your Cargo.toml:

```toml
[dependencies]
ws_log = { path = "path/to/ws_log" }
log = "0.4"
```

Example usage:

```rust
use log::{debug, error, info, warn, LevelFilter};

fn main() {
    // Initialize the logger with debug level
    ws_log::init_with_level(LevelFilter::Debug).unwrap();

    // Log messages at different levels
    let tag = "example";
    debug!(target: tag, "This is a debug message from <PERSON>ust");
    info!(target: tag, "This is an info message from Rust");
    warn!(target: tag, "This is a warning message from Rust");
    error!(target: tag, "This is an error message from Rust");

    // You can also use formatted messages
    let value = 42;
    info!(target: tag, "The answer is {}", value);
}
```

## Usage from C/C++

Include the header file:

```c
#include "ws_log.h"
```

Example usage:

```c
#include <stdio.h>
#include "ws_log.h"

int main() {
    // Initialize the logger with debug level (1)
    if (ws_log_init_with_level(1) != 0) {
        fprintf(stderr, "Failed to initialize logger\n");
        return 1;
    }
    
    // Log messages at different levels
    const char* tag = "c_example";
    ws_log_debug(tag, "This is a debug message from C");
    ws_log_info(tag, "This is an info message from C");
    ws_log_warn(tag, "This is a warning message from C");
    ws_log_error(tag, "This is an error message from C");
    
    return 0;
}
```

### Building with CMake

```bash
mkdir build && cd build
cmake ..
make
```

## License

[Your license information here]
