[package]
name = "ws_log"
version = "0.1.0"
edition = "2021"
rust-version = "1.75.0"
description = "Workspace log interface for Rust"

# Library configuration
[lib]
name = "ws_log"
# Build both dynamic and static libraries
crate-type = ["cdylib", "rlib", "staticlib"]
path = "src/lib.rs"

[dependencies]
log = { version = "0.4", features = ["std"] }
libc = "0.2"
cxx = "1.0"
once_cell = "1.8"

[build-dependencies]
cxx-build = "1.0"
cc = "1.0"

[[example]]
name = "log_example"
path = "examples/log_example.rs"

[[test]]
name = "log_test"
path = "tests/log_test.rs"
