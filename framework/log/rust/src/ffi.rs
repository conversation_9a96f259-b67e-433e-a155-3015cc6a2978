#[cxx::bridge(namespace = "ws::log::bridge")]
mod ffi {
    unsafe extern "C++" {
        // We'll use a bridge header instead of directly including log_printf.hpp
        include!("bridge.h");

        fn Debug(tag: &str, fmt: &str) -> ();
        fn Info(tag: &str, fmt: &str) -> ();
        fn Warn(tag: &str, fmt: &str) -> ();
        fn Error(tag: &str, fmt: &str) -> ();
        // fn Fatal(tag: &str, fmt: &str) -> ();
    }
}

pub use ffi::*;
