use log::{Level, LevelFilter, Log, Metadata, Record};
use once_cell::sync::Lazy;

mod ffi;

static LOGGER: Lazy<Logger> = Lazy::new(|| Logger {
    default_tag: "ws_log",
    default_level: LevelFilter::Info,
});

pub struct Logger {
    default_tag: &'static str,
    default_level: LevelFilter,
}

impl Logger {
    pub fn new() -> Self {
        Self {
            default_tag: "ws_log",
            default_level: LevelFilter::Info,
        }
    }

    pub fn init() -> Result<(), log::SetLoggerError> {
        log::set_logger(&*LOGGER).map(|()| {
            log::set_max_level(LOGGER.default_level);
        })
    }

    pub fn with_level(mut self, level: LevelFilter) -> Self {
        self.default_level = level;
        self
    }

    pub fn with_tag(mut self, tag: &'static str) -> Self {
        self.default_tag = tag;
        self
    }
}

impl Log for Logger {
    fn enabled(&self, metadata: &Metadata) -> bool {
        metadata.level() <= self.default_level
    }

    fn log(&self, record: &Record) {
        if self.enabled(record.metadata()) {
            let target = if !record.target().is_empty() {
                record.target()
            } else {
                record.module_path().unwrap_or_default()
            };

            let message = format!("{}", record.args());

            // Call the C++ function to log the message based on the log level
            match record.level() {
                Level::Error => ffi::Error(target, &message),
                Level::Warn => ffi::Warn(target, &message),
                Level::Info => ffi::Info(target, &message),
                Level::Debug => ffi::Debug(target, &message),
                Level::Trace => ffi::Debug(target, &message), // Use Debug for Trace level
            }
        }
    }

    fn flush(&self) {}
}

// Convenience function to initialize the logger with default settings
pub fn init() -> Result<(), log::SetLoggerError> {
    Logger::init()
}

// Initialize with a specific log level
pub fn init_with_level(level: LevelFilter) -> Result<(), log::SetLoggerError> {
    log::set_logger(&*LOGGER).map(|()| {
        log::set_max_level(level);
    })
}
