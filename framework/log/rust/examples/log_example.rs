use log::{debug, error, info, warn, LevelFilter};

fn main() {
    // Initialize the logger with debug level
    ws_log::init_with_level(LevelFilter::Debug).unwrap();

    // Log messages at different levels
    let tag = "example";
    debug!(target: tag, "This is a debug message from Rust");
    info!(target: tag, "This is an info message from Rust");
    warn!(target: tag, "This is a warning message from Rust");
    error!(target: tag, "This is an error message from Rust");

    // You can also use formatted messages
    let value = 42;
    info!(target: tag, "The answer is {}", value);

    // If no target is specified, the module path is used
    debug!("Debug message without explicit target");
}
