#ifndef LOG_REGISTER_HPP_
#define LOG_REGISTER_HPP_

#include <memory>

#include "log/log_level.hpp"
#include "utils/macros.hpp"

namespace ws::log {
class Logger;

class Register {
public:
    static Register& Instance();
    DISABLE_COPY_AND_MOVE(Register)

    Logger* GetDefaultRaw();

#ifdef BUILD_TESTING
    void SetLogLevel(level::LogLevel const lvl);
void SetDefaultLogger(std::shared_ptr<Logger> new_default_logger)
#endif  // BUILD_TESTING

    private : Register();
    ~Register();

    class RegisterImpl;
    std::unique_ptr<RegisterImpl> pimpl_;
};

}  // namespace ws::log

#endif  // LOG_REGISTER_HPP_