#ifndef LOG_COMMON_MACRO_HPP_
#define LOG_COMMON_MACRO_HPP_

// Use macro expansion to create, for each use of LOG_EVERY_N(), static variable with the __LINE__ expansion as part of
// the variable name.
#define LOG_EVERY_N_VARNAME(base, line) LOG_EVERY_N_VARNAME_CONCAT(base, line)
#define LOG_EVERY_N_VARNAME_CONCAT(base, line) base##line

#define LOG_OCCURRENCES_MOD_N LOG_EVERY_N_VARNAME(occurrences_mod_n, __LINE__)

#endif  // LOG_COMMON_MACRO_HPP_