#ifndef LOG_LOG_STREAM_HPP_
#define LOG_LOG_STREAM_HPP_

#include <atomic>
#include <sstream>

#include "log/log_printf.hpp"
#include "utils/macros.hpp"

namespace ws::log {

class LogStreamVodify {
public:
    LogStreamVodify() = default;
    void operator&(std::ostream&) {}
};

using LogFunc = void (*)(char const* const tag, char const* fmt, ...) noexcept;

template <LogFunc func>
class LogStreamTemplate final {
public:
    LogStreamTemplate(char const* const tag) : tag_{tag} {}
    ~LogStreamTemplate() { func(tag_, stream_.str().c_str()); }

    DISABLE_COPY_AND_MOVE(LogStreamTemplate)

    std::ostringstream& Stream() { return stream_; }

private:
    char const* tag_;
    std::ostringstream stream_;
};

#define LOG_STREAM_DEBUG(tag) (ws::log::LogStreamTemplate<ws::log::Debug>(tag)).Stream()
#define LOG_STREAM_INFO(tag) (ws::log::LogStreamTemplate<ws::log::Info>(tag)).Stream()
#define LOG_STREAM_WARN(tag) (ws::log::LogStreamTemplate<ws::log::Warn>(tag)).Stream()
#define LOG_STREAM_ERROR(tag) (ws::log::LogStreamTemplate<ws::log::Error>(tag)).Stream()
#define LOG_STREAM_FATAL(tag) (ws::log::LogStreamTemplate<ws::log::Fatal>(tag)).Stream()

// 满足条件打印
#define LOG_STREAM_DEBUG_IF(condition, tag) !(condition) ? (void)0 : ws::log::LogStreamVodify() & LOG_STREAM_DEBUG(tag)

#define LOG_STREAM_INFO_IF(condition, tag) !(condition) ? (void)0 : ws::log::LogStreamVodify() & LOG_STREAM_INFO(tag)

#define LOG_STREAM_WARN_IF(condition, tag) !(condition) ? (void)0 : ws::log::LogStreamVodify() & LOG_STREAM_WARN(tag)

#define LOG_STREAM_ERROR_IF(condition, tag) !(condition) ? (void)0 : ws::log::LogStreamVodify() & LOG_STREAM_ERROR(tag)

#define LOG_STREAM_FATAL_IF(condition, tag) !(condition) ? (void)0 : ws::log::LogStreamVodify() & LOG_STREAM_FATAL(tag)

// private
#define LOG_STREAM_COUNTER LOG_EVERY_N_VARNAME(counter_, __LINE__)

#define LOG_STREAM_IF_EVERY_N(log_macro, confition, n, tag)                                 \
    static std::atomic<int> LOG_OCCURRENCES_MOD_N(0);                                       \
    int LOG_STREAM_COUNTER(0);                                                              \
    if (condition) {                                                                        \
        LOG_STREAM_COUNTER = LOG_OCCURRENCES_MOD_N.fetch_add(1, std::memory_order_relaxed); \
        if (LOG_STREAM_COUNTER >= n) {                                                      \
            LOG_OCCURRENCES_MOD_N.fetch_sub(n, std::memory_order_relaxed);                  \
            LOG_STREAM_COUNTER -= n;                                                        \
        }                                                                                   \
    }                                                                                       \
    if (confition && LOG_STREAM_COUNTER % n == 0) (log_macro(tag))

// 每n条打印
#define LOG_STREAM_DEBUG_EVERY_N(n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_DEBUG, true, n, tag)

#define LOG_STREAM_INFO_EVERY_N(n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_INFO, true, n, tag)

#define LOG_STREAM_WARN_EVERY_N(n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_WARN, true, n, tag)

#define LOG_STREAM_ERROR_EVERY_N(n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_ERROR, true, n, tag)

#define LOG_STREAM_FATAL_EVERY_N(n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_FATAL, true, n, tag)

// 满足条件每n条打印
#define LOG_STREAM_DEBUG_IF_EVERY_N(condition, n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_DEBUG, condition, n, tag)

#define LOG_STREAM_INFO_IF_EVERY_N(condition, n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_INFO, condition, n, tag)

#define LOG_STREAM_WARN_IF_EVERY_N(condition, n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_WARN, condition, n, tag)

#define LOG_STREAM_ERROR_IF_EVERY_N(condition, n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_ERROR, condition, n, tag)

#define LOG_STREAM_FATAL_IF_EVERY_N(condition, n, tag) LOG_STREAM_IF_EVERY_N(LOG_STREAM_FATAL, condition, n, tag)

}  // namespace ws::log

#endif  // LOG_LOG_STREAM_HPP_