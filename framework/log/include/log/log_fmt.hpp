#ifndef LOG_LOG_FMT_HPP_
#define LOG_LOG_FMT_HPP_

#include <atomic>
#include <string_view>

#include "log/common_macro.hpp"
#include "log/logger.hpp"

namespace ws::log {

Logger *DefaultLoggerRaw();

/////////////////////////////// c++ fmt style interface /////////////////////////////////

template <typename... Args>
inline void DebugFmt(std::string_view tag, std::string_view fmt, Args &&...args) {
    DefaultLoggerRaw()->Debug(tag, fmt, std::forward<Args>(args)...);
}

template <typename... Args>
inline void InfoFmt(std::string_view tag, std::string_view fmt, Args &&...args) {
    DefaultLoggerRaw()->Info(tag, fmt, std::forward<Args>(args)...);
}

template <typename... Args>
inline void WarnFmt(std::string_view tag, std::string_view fmt, Args &&...args) {
    DefaultLoggerRaw()->Warn(tag, fmt, std::forward<Args>(args)...);
}

template <typename... Args>
inline void ErrorFmt(std::string_view tag, std::string_view fmt, Args &&...args) {
    DefaultLoggerRaw()->Error(tag, fmt, std::forward<Args>(args)...);
}

template <typename... Args>
inline void FatalFmt(std::string_view tag, std::string_view fmt, Args &&...args) {
    DefaultLoggerRaw()->Fatal(tag, fmt, std::forward<Args>(args)...);
}

// 满足条件打印
#define LOG_DEBUG_FMT_IF(confition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::DebugFmt(tag, fmt, ##__VA_ARGS__)

#define LOG_INFO_FMT_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::InfoFmt(tag, fmt, ##__VA_ARGS__)

#define LOG_WARN_FMT_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::WarnFmt(tag, fmt, ##__VA_ARGS__)

#define LOG_ERROR_FMT_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::ErrorFmt(tag, fmt, ##__VA_ARGS__)

#define LOG_FATAL_FMT_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::FatalFmt(tag, fmt, ##__VA_ARGS__)

#define LOG_FMT_IF_EVERY_N(log_func, confition, n, tag, fmt, ...)                         \
    static std::atomic<int> LOG_OCCURRENCES_MOD_N(0);                                     \
    if (condition) {                                                                      \
        int curr_counter = LOG_OCCURRENCES_MOD_N.fetch_add(1, std::memory_order_relaxed); \
        if (curr_counter >= n) {                                                          \
            LOG_OCCURRENCES_MOD_N.fetch_sub(n, std::memory_order_relaxed);                \
            curr_counter -= n;                                                            \
        }                                                                                 \
        if (curr_counter % n == 0) {                                                      \
            log_func(tag, fmt, ##__VA_ARGS__);                                            \
        }                                                                                 \
    }

// 每n条打印
#define LOG_DEBUG_FMT_EVERY_N(n, tag, fmt, ...) LOG_FMT_IF_EVERY_N(ws::log::DebugFmt, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_INFO_FMT_EVERY_N(n, tag, fmt, ...) LOG_FMT_IF_EVERY_N(ws::log::InfoFmt, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_WARN_FMT_EVERY_N(n, tag, fmt, ...) LOG_FMT_IF_EVERY_N(ws::log::WarnFmt, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_ERROR_FMT_EVERY_N(n, tag, fmt, ...) LOG_FMT_IF_EVERY_N(ws::log::ErrorFmt, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_FATAL_FMT_EVERY_N(n, tag, fmt, ...) LOG_FMT_IF_EVERY_N(ws::log::FatalFmt true, n, tag, fmt, ##__VA_ARGS__)

// 满足条件每n条打印
#define LOG_DEBUG_FMT_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_FMT_IF_EVERY_N(ws::log::DebugFmt, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_INFO_FMT_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_FMT_IF_EVERY_N(ws::log::InfoFmt, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_WARN_FMT_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_FMT_IF_EVERY_N(ws::log::WarnFmt, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_ERROR_FMT_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_FMT_IF_EVERY_N(ws::log::ErrorFmt, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_FATAL_FMT_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_FMT_IF_EVERY_N(ws::log::FatalFmt, condition, n, tag, fmt, ##__VA_ARGS__)

}  // namespace ws::log

#endif  // LOG_LOG_FMT_HPP_