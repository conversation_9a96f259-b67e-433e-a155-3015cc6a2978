#ifndef LOG_LOG_PRINTF_HPP_
#define LOG_LOG_PRINTF_HPP_

#include <atomic>

#include "log/common_macro.hpp"

namespace ws::log {

/////////////////////////////// c printf style interface /////////////////////////////////

void Debug(char const* const tag, char const* fmt, ...) noexcept;

void Info(char const* const tag, char const* fmt, ...) noexcept;
void Warn(char const* const tag, char const* fmt, ...) noexcept;
void Error(char const* const tag, char const* fmt, ...) noexcept;
void Fatal(char const* const tag, char const* fmt, ...) noexcept;

// 满足条件打印
#define LOG_DEBUG_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::Debug(tag, fmt, ##__VA_ARGS__)

#define LOG_INFO_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::Info(tag, fmt, ##__VA_ARGS__)

#define LOG_WARN_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::Warn(tag, fmt, ##__VA_ARGS__)

#define LOG_ERROR_IF(condition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::Error(tag, fmt, ##__VA_ARGS__)

#define LOG_FATAL_IF(confition, tag, fmt, ...) !(condition) ? (void)0 : ws::log::Fatal(tag, fmt, ##__VA_ARGS__)

#define LOG_IF_EVERY_N(log_func, confition, n, tag, fmt, ...)                             \
    static std::atomic<int> LOG_OCCURRENCES_MOD_N(0);                                     \
    if (condition) {                                                                      \
        int curr_counter = LOG_OCCURRENCES_MOD_N.fetch_add(1, std::memory_order_relaxed); \
        if (curr_counter >= n) {                                                          \
            LOG_OCCURRENCES_MOD_N.fetch_sub(n, std::memory_order_relaxed);                \
            curr_counter -= n;                                                            \
        }                                                                                 \
        if (curr_counter % n == 0) {                                                      \
            log_func(tag, fmt, ##__VA_ARGS__);                                            \
        }                                                                                 \
    }

// 每n条打印
#define LOG_DEBUG_EVERY_N(n, tag, fmt, ...) LOG_IF_EVERY_N(ws::log::Debug, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_INFO_EVERY_N(n, tag, fmt, ...) LOG_IF_EVERY_N(ws::log::Info, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_WARN_EVERY_N(n, tag, fmt, ...) LOG_IF_EVERY_N(ws::log::Warn, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_ERROR_EVERY_N(n, tag, fmt, ...) LOG_IF_EVERY_N(ws::log::Error, true, n, tag, fmt, ##__VA_ARGS__)

#define LOG_FATAL_EVERY_N(n, tag, fmt, ...) LOG_IF_EVERY_N(ws::log::Fatal, true, n, tag, fmt, ##__VA_ARGS__)

// 满足条件每n条打印
#define LOG_DEBUG_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_IF_EVERY_N(ws::log::Debug, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_INFO_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_IF_EVERY_N(ws::log::Info, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_WARN_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_IF_EVERY_N(ws::log::Warn, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_ERROR_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_IF_EVERY_N(ws::log::Error, condition, n, tag, fmt, ##__VA_ARGS__)

#define LOG_FATAL_IF_EVERY_N(condition, n, tag, fmt, ...) \
    LOG_IF_EVERY_N(ws::log::Fatal, condition, n, tag, fmt, ##__VA_ARGS__)

}  // namespace ws::log

#endif  // LOG_LOG_PRINTF_HPP_