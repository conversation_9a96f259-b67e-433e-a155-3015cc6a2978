#ifndef LOG_LOGGER_HPP_
#define LOG_LOGGER_HPP_

#include <fmt/format.h>

#include <array>
#include <memory>
#include <string>
#include <string_view>

#include "log/log_level.hpp"
#include "utils/macros.hpp"

namespace ws::log::details {
class LogMsg;
}  // namespace ws::log::details

namespace ws::log::sinks {
class Sink;
}  // namespace ws::log::sinks

namespace ws::log {

class Logger {
public:
    explicit Logger(std::string logger_name, std::shared_ptr<sinks::Sink> sink);
    virtual ~Logger() = default;

    DISABLE_COPY(Logger)

    /* c style interface*/
    void Log(level::LogLevel const lvl, char const* const tag, char const* fmt, va_list args);
    void Debug(char const* const tag, char const* fmt, va_list args);
    void Info(char const* const tag, char const* fmt, va_list args);
    void Warn(char const* const tag, char const* fmt, va_list args);
    void Error(char const* const tag, char const* fmt, va_list args);
    void Fatal(char const* const tag, char const* fmt, va_list args);

    /* c++ fmt interface */
    template <typename... Args>
    void Debug(std::string_view tag, std::string_view fmt, Args&&... args) {
        Log(level::LogLevel::kDebug, tag, fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void Info(std::string_view tag, std::string_view fmt, Args&&... args) {
        Log(level::LogLevel::kInfo, tag, fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void Warn(std::string_view tag, std::string_view fmt, Args&&... args) {
        Log(level::LogLevel::kWarn, tag, fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void Error(std::string_view tag, std::string_view fmt, Args&&... args) {
        Log(level::LogLevel::kError, tag, fmt, std::forward<Args>(args)...);
    }

    template <typename... Args>
    void Fatal(std::string_view tag, std::string_view fmt, Args&&... args) {
        Log(level::LogLevel::kFatal, tag, fmt, std::forward<Args>(args)...);
    }

    void SetLogLevel(level::LogLevel const lvl);

    void PeriodicFlush();

protected:
    template <typename... Args>
    void TryLog(level::LogLevel const lvl, std::string_view tag, std::string_view fmt, Args&&... args) {
        if (!ShouldLog(lvl)) {
            return;
        }

        std::array<char, kBufferSize> buf{};
        fmt::vformat_to_n(buf.data(), kBufferSize - 1, fmt::basic_string_view<char>(fmt),
                          fmt::make_format_args(std::forward<Args>(args)...));

        LogIt(lvl, tag, buf.data());
    }

    template <typename... Args>
    void Log(level::LogLevel const lvl, std::string_view tag, std::string_view fmt, Args&&... args) {
        TryLog(lvl, tag, fmt, std::forward<Args>(args)...);
    }

    [[nodiscard]] bool ShouldLog(level::LogLevel const lvl) const;

    void LogIt(details::LogMsg const& log_msg);

    void LogIt(level::LogLevel const lvl, std::string_view tag, std::string_view msg);

    // 用于 async_logger 子类重写
    virtual void SinkIt(details::LogMsg const& log_msg);

    // 用于 async_logger 子类重写
    virtual void FlushIt();

    [[nodiscard]]
    bool ShouldFlush(level::LogLevel const lvl) const;

    void TryLog(level::LogLevel const lvl, char const* const tag, char const* fmt, va_list args);

    std::string logger_name_;

    std::shared_ptr<sinks::Sink> sink_;

    int level_;
    int flush_level_;

    inline static constexpr int32_t const kBufferSize{1024};
};

}  // namespace ws::log

#endif  // LOG_LOGGER_HPP_