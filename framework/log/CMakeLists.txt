cmake_minimum_required(VERSION 3.23)
project(log LANGUAGES CXX)

option(USE_ANDROID "Use android log as global log configure." ON)

file(GLOB ${PROJECT_NAME}_SRC src/*.cpp src/details/*.cpp src/sinks/*.cpp)
file(GLOB_RECURSE ${PROJECT_NAME}_HEADER include/*.hpp include/*.h)

add_library(${PROJECT_NAME} 
    SHARED 
        ${${PROJECT_NAME}_SRC}
)
target_include_module_directories(
    ${PROJECT_NAME}
    PUBLIC utils
)
target_include_directories(${PROJECT_NAME} 
    PUBLIC 
        include private
)
target_link_libraries(${PROJECT_NAME}
    PUBLIC
        alog
        fmt
    PRIVATE
        pthread
)

install(TARGETS ${PROJECT_NAME}
    LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}
)

if (BUILD_EXAMPLE)
    add_subdirectory(examples)
endif()

