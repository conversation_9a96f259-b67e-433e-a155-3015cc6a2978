#include <chrono>
#include <thread>

#include "log/log.hpp"

namespace {
constexpr char const* kLogTag{"LogExample"};
constexpr int32_t kLoopCnt{50};
}  // namespace

void CstyleInterface() {
    std::string const str{"abc"};
    // ws::log::Debug(kLogTag, "c style interface, str: %s", str.c_str());
    // ws::log::Info(kLogTag, "c style interface, str: %s", str.c_str());
    ws::log::Warn(kLogTag, "c style interface, str: %s", str.c_str());
    // ws::log::Error(kLogTag, "c style interface, str: %s", str.c_str());
    // ws::log::Fatal(kLogTag, "c style interface, str: %s", str.c_str());
    std::this_thread::sleep_for(std::chrono::seconds(2));
    ws::log::Warn(kLogTag, "2");
    while (true) {
    };
}

void CPlusPlusFmtInterface() {
    std::string const str{"abc"};
    ws::log::DebugFmt(kLogTag, "c++ fmt interface, str: {}", str);
    ws::log::InfoFmt(kLogTag, "c++ fmt interface, str: {}", str);
    ws::log::WarnFmt(kLogTag, "c++ fmt interface, str: {}", str);
    ws::log::ErrorFmt(kLogTag, "c++ fmt interface, str: {}", str);
    ws::log::FatalFmt(kLogTag, "c++ fmt interface, str: {}", str);
}

void CPlusPlusStreamInterface() {
    std::string const str{"abc"};
    double const d{1.0};
    float const f{1.0F};
    unsigned int const ui{10};
    uint64_t const uli{10UL};

    LOG_STREAM_DEBUG(kLogTag) << "LOG_STREAM_DEBUG, str: " << str << ", d: " << d << ", f: " << f << ", ui: " << ui
                              << ", uli: " << uli;
    LOG_STREAM_INFO(kLogTag) << "LOG_STREAM_INFO, str: " << str << ", d: " << d << ", f: " << f << ", ui: " << ui
                             << ", uli: " << uli;
    LOG_STREAM_WARN(kLogTag) << "LOG_STREAM_WARN, str: " << str << ", d: " << d << ", f: " << f << ", ui: " << ui
                             << ", uli: " << uli;
    LOG_STREAM_ERROR(kLogTag) << "LOG_STREAM_ERROR, str: " << str << ", d: " << d << ", f: " << f << ", ui: " << ui
                              << ", uli: " << uli;
    LOG_STREAM_FATAL(kLogTag) << "LOG_STREAM_FATAL, str: " << str << ", d: " << d << ", f: " << f << ", ui: " << ui
                              << ", uli: " << uli;
}

int main() {
    CstyleInterface();
    // CPlusPlusFmtInterface();
    // CPlusPlusStreamInterface();
}
