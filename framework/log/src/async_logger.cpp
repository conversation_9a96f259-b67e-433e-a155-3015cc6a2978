#include "async_logger.hpp"

#include "sinks/sink.hpp"

namespace ws::log {
AsyncLogger::AsyncLogger(std::string const& logger_name, std::shared_ptr<sinks::Sink> sink,
                         std::weak_ptr<details::AsyncThreadPool> thread_pool, AsyncOverflowPolicy overflow_policy)
    : Logger(logger_name, std::move(sink)), thread_pool_{std::move(thread_pool)}, overflow_policy_{overflow_policy} {}

void AsyncLogger::SinkIt(details::LogMsg const& log_msg) {
    if (auto thread_pool_ptr = thread_pool_.lock()) {
        syslog(LOG_INFO, "PeriodicFlusher Start Async sinkit");
        thread_pool_ptr->PostLog(shared_from_this(), log_msg, overflow_policy_);
    }

    if (ShouldFlush(log_msg.level_)) {
        FlushIt();
    }
}

void AsyncLogger::BackendSinkIt(details::LogMsg const& log_msg) { sink_->Log(log_msg); }

void AsyncLogger::FlushIt() {
    if (auto thread_pool_ptr = thread_pool_.lock()) {
        thread_pool_ptr->PostFlush(shared_from_this(), overflow_policy_);
    }
}

void AsyncLogger::BackendFlushIt() { sink_->Flush(); }

}  // namespace ws::log
