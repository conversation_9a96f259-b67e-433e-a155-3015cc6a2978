#include "register_impl.hpp"

#include <syslog.h>
#include <unistd.h>  // getpid()

#include <array>
#include <filesystem>
#include <map>

#include "async_logger.hpp"
#include "async_overflow_policy.hpp"
#include "details/periodic_flusher.hpp"
#include "sinks/android_sink.hpp"
#include "sinks/null_sink.hpp"
#include "sinks/rotating_file_sink.hpp"
#include "sinks/stdout_sink.hpp"

namespace {
constexpr char const* kLogFilePrefix{"/log/app/"};

constexpr char const* kLogModeAsync{"LOG_MODE_ASYNC"};

constexpr char const* KLogWriteMode{"LOG_WRITE_MODE"};

constexpr char const* kLogAsyncQueueSlot{"LOG_ASYNC_QUEUE_SLOT"};

constexpr char const* kLogFileSizeMb{"LOG_FILE_SIZE_MB"};

constexpr char const* kLogLevel{"LOG_LEVEL"};

constexpr char const* kSinkToSingleFile{"singlefile"};

constexpr char const* kSinkToConsole{"console"};

constexpr char const* kSinkToNull{"null"};

constexpr std::size_t kDefaultAsyncQueueSlot{51200UL};

constexpr std::size_t kAsyncQueueSlotMinSize{1000UL};

constexpr std::size_t kAsyncQueueSlotMaxSize{100'000UL};

constexpr std::size_t kDefaultLogFileSizeMb{20UL};

constexpr std::size_t kLogFileMinSizeMb{1UL};

constexpr std::size_t kLogFileMaxSizeMb{200UL};

constexpr std::size_t KoneMb{static_cast<std::size_t>(1024) * 1024};

}  // namespace

namespace ws::log {

Register::RegisterImpl::RegisterImpl()
    : periodic_flusher_{std::make_unique<details::PeriodicFlusher>([this]() { PeriodicFlush(); })} {
    LoadLogEnvCfg();
}

// Register::RegisterImpl::RegisterImpl() { LoadLogEnvCfg(); }

Logger* Register::RegisterImpl::GetDefaultRaw() {
    if (default_logger_ == nullptr) {
        return nullptr;
    }

    return default_logger_.get();
}

void Register::RegisterImpl::LoadLogEnvCfg() {
    LogMode const log_mode = GetLogModeFromEnv();
    SinkMode const sink_mode = GetWriteModeFromEnv();

    std::size_t async_queue_slot{0};
    if (log_mode == LogMode::kAsync) {
        async_queue_slot = GetAsyncQueueSlotFromEnv();
    }

    std::size_t log_file_size{0};
    if (sink_mode == SinkMode::kSinkToSingleFile) {
        log_file_size = GetLogFileSizeFromEnv();
    }

    CreateDefaultLogger(log_mode, sink_mode, async_queue_slot, log_file_size);

    char const* env_log_level{std::getenv(kLogLevel)};
    if (env_log_level != nullptr) {
        std::string s{env_log_level};
        std::transform(s.begin(), s.end(), s.begin(), ::tolower);

        std::map<std::string, level::LogLevel> const env2lvl = {{"debug", level::LogLevel::kDebug},
                                                                {"info", level::LogLevel::kInfo},
                                                                {"warn", level::LogLevel::kWarn},
                                                                {"error", level::LogLevel::kError},
                                                                {"fatal", level::LogLevel::kFatal}};

        auto const& it{env2lvl.find(s)};
        if (it != env2lvl.end()) {
            default_logger_->SetLogLevel(it->second);
        }
    }
}

LogMode Register::RegisterImpl::GetLogModeFromEnv() {
    if (IsEnabled(kLogModeAsync, false)) {
        return LogMode::kAsync;
    }

    return LogMode::kSync;
}

SinkMode Register::RegisterImpl::GetWriteModeFromEnv() {
    char const* env = std::getenv(KLogWriteMode);

    if (env != nullptr) {
        std::string log_write_mode{env};
        std::transform(log_write_mode.begin(), log_write_mode.end(), log_write_mode.begin(), ::tolower);
        if (log_write_mode == kSinkToSingleFile) {
            return SinkMode::kSinkToSingleFile;
        } else if (log_write_mode == kSinkToConsole) {
            return SinkMode::kSinkToConsole;
        } else if (log_write_mode == kSinkToNull) {
            return SinkMode::kSinkToNull;
        }
    }
    // 默认模式是kSinkToAndroidv
    return SinkMode::kSinkToAndroid;
}

bool Register::RegisterImpl::IsEnabled(std::string const& env_name, bool default_value) {
    char const* env = std::getenv(env_name.c_str());
    if (env == nullptr) {
        return default_value;
    }

    static constexpr uint32_t kFlagCount{5U};
    static constexpr std::array<std::string_view, kFlagCount> kFlags{"TRUE", "true", "1", "ON", "on"};
    return std::any_of(kFlags.cbegin(), kFlags.cend(), [&env](auto const& flag) { return flag == env; });
}

std::size_t Register::RegisterImpl::GetAsyncQueueSlotFromEnv() {
    char const* async_queue_slot_env = std::getenv(kLogAsyncQueueSlot);
    if (async_queue_slot_env == nullptr) {
        return kDefaultAsyncQueueSlot;
    }

    std::size_t async_queue_slot{0};
    try {
        async_queue_slot = std::stol(async_queue_slot_env);
        if (!AsyncQueueSlotValidityCheck(async_queue_slot)) {
            async_queue_slot = kDefaultAsyncQueueSlot;
        }
    } catch (std::invalid_argument& e) {
        async_queue_slot = kDefaultAsyncQueueSlot;
    } catch (std::out_of_range& e) {
        async_queue_slot = kDefaultAsyncQueueSlot;
    }

    return async_queue_slot;
}

std::size_t Register::RegisterImpl::GetLogFileSizeFromEnv() {
    char const* log_file_size_mb_env = std::getenv(kLogFileSizeMb);
    if (log_file_size_mb_env == nullptr) {
        return kDefaultLogFileSizeMb * KoneMb;
    }

    std::size_t log_file_size_mb{0};
    try {
        log_file_size_mb = std::stol(log_file_size_mb_env);
        if (!LogFileSizeValidityCheck(log_file_size_mb)) {
            log_file_size_mb = kDefaultLogFileSizeMb;
        }
    } catch (std::invalid_argument& e) {
        log_file_size_mb = kDefaultLogFileSizeMb;
    } catch (std::out_of_range& e) {
        log_file_size_mb = kDefaultLogFileSizeMb;
    }

    return log_file_size_mb * KoneMb;
}

bool Register::RegisterImpl::AsyncQueueSlotValidityCheck(std::size_t const async_queue_slot) {
    return ((async_queue_slot >= kAsyncQueueSlotMinSize) && (async_queue_slot <= kAsyncQueueSlotMaxSize));
}

bool Register::RegisterImpl::LogFileSizeValidityCheck(std::size_t const log_file_size) {
    return ((log_file_size >= kLogFileMinSizeMb) && (log_file_size <= kLogFileMaxSizeMb));
}

std::string Register::RegisterImpl::GetProcessNameFromProc(pid_t const pid) {
    std::filesystem::path const exe_link = std::filesystem::read_symlink("/proc/" + std::to_string(pid) + "/exe");
    return exe_link.filename().string();
}

void Register::RegisterImpl::CreateDefaultLogger(LogMode const log_mode, SinkMode const sink_mode,
                                                 std::size_t const async_queue_slot, std::size_t const log_file_size) {
    if (log_mode == LogMode::kSync) {
        switch (sink_mode) {
            case SinkMode::kSinkToConsole: {
                auto stdout_sink = std::make_shared<sinks::StdoutSink>();
                default_logger_ = std::make_shared<Logger>("stdout_logger", std::move(stdout_sink));
                syslog(LOG_WARNING, "create sync_stdout_logger");
                break;
            }
            case SinkMode::kSinkToSingleFile: {
                auto const log_file_name = GetProcessNameFromProc(getpid());
                auto rotating_file_sink =
                    std::make_shared<sinks::RotatingFileSink>(kLogFilePrefix, log_file_name, log_file_size);
                default_logger_ = std::make_shared<Logger>("file_logger", std::move(rotating_file_sink));
                syslog(LOG_WARNING, "create sync_file_logger");
                break;
            }
            case SinkMode::kSinkToNull: {
                auto null_sink = std::make_shared<sinks::NullSink>();
                default_logger_ = std::make_shared<Logger>("null_logger", std::move(null_sink));
                syslog(LOG_WARNING, "create sync_null_logger");
                break;
            }
            default: {
                auto android_sink = std::make_shared<sinks::AndroidSink<log_id::LOG_ID_MAIN>>();
                default_logger_ = std::make_shared<Logger>("android_logger", std::move(android_sink));
                syslog(LOG_WARNING, "create sync_android_logger");
                break;
            }
        }
    } else if (log_mode == LogMode::kAsync) {
        switch (sink_mode) {
            case SinkMode::kSinkToConsole: {
                auto constexpr kWorkerCount{1UL};
                thread_pool_ = std::make_shared<details::AsyncThreadPool>(kWorkerCount, async_queue_slot);
                auto stdout_sink = std::make_shared<sinks::StdoutSink>();
                default_logger_ =
                    std::make_shared<AsyncLogger>("async_stdout_logger", std::move(stdout_sink),
                                                  std::move(thread_pool_), AsyncOverflowPolicy::kOverrunOldest);
                syslog(LOG_WARNING, "create async_stdout_logger, worker_count: %lu, async_log_queue_size: %lu",
                       kWorkerCount, async_queue_slot);
                break;
            }
            case SinkMode::kSinkToSingleFile: {
                auto const log_file_name = GetProcessNameFromProc(getpid());
                auto constexpr kWorkerCount{1UL};
                thread_pool_ = std::make_shared<details::AsyncThreadPool>(kWorkerCount, async_queue_slot);
                auto rotating_file_sink =
                    std::make_shared<sinks::RotatingFileSink>(kLogFilePrefix, log_file_name, log_file_size);
                default_logger_ =
                    std::make_shared<AsyncLogger>("async_rotating_file_logger", std::move(rotating_file_sink),
                                                  std::move(thread_pool_), AsyncOverflowPolicy::kOverrunOldest);
                syslog(LOG_WARNING,
                       "create async_rotating_file_logger, worker_count: %lu, async_log_queue_size: %lu, "
                       "log_file_size: %lu, log_file_name: %s",
                       kWorkerCount, async_queue_slot, log_file_size, log_file_name.c_str());
                break;
            }
            case SinkMode::kSinkToNull: {
                auto constexpr kWorkerCount{1UL};
                thread_pool_ = std::make_shared<details::AsyncThreadPool>(kWorkerCount, async_queue_slot);
                auto null_sink = std::make_shared<sinks::NullSink>();
                default_logger_ =
                    std::make_shared<AsyncLogger>("async_null_logger", std::move(null_sink), std::move(thread_pool_),
                                                  AsyncOverflowPolicy::kOverrunOldest);
                syslog(LOG_WARNING, "create async_null_logger, worker_count: %lu, async_log_queue_size: %lu",
                       kWorkerCount, async_queue_slot);
                break;
            }
            default: {
                syslog(LOG_ERR, "log configuration error, abort");
                abort();
                break;
            }
        }
    }
}

void Register::RegisterImpl::PeriodicFlush() {
    if (default_logger_ != nullptr) {
        default_logger_->PeriodicFlush();
    }
}

#ifdef BUILD_TESTING
void Register::RegisterImpl::SetLogLevel(level::LogLevel const lvl) {
    std::lock_guard<std::mutex> const lock{mt_};
    default_logger_->SetLevel(lvl);
}

void Register::RegisterImpl::SetDefaultLogger(std::shared_ptr<Logger> new_default_logger) {
    std::lock_guard<std::mutex> const lock{mt_};
    default_logger_ = std::move(new_default_logger);
}
#endif  // BUILD_TESTING

}  // namespace ws::log
