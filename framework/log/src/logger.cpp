#include "log/logger.hpp"

#include <syslog.h>

#include "sinks/sink.hpp"

namespace {
constexpr int kDefaultLogLevel = static_cast<int>(ws::log::level::LogLevel::kDebug);
}  // namespace

namespace ws::log {
Logger::Logger(std::string logger_name, std::shared_ptr<sinks::Sink> sink)
    : logger_name_{std::move(logger_name)},
      sink_{std::move(sink)},
      level_{kDefaultLogLevel},
      flush_level_{static_cast<int>(level::LogLevel::kFatal)} {}

/* c style implement */
void Logger::Log(level::LogLevel const lvl, char const* const tag, char const* fmt, va_list args) {
    TryLog(lvl, tag, fmt, args);
}

void Logger::Debug(char const* const tag, char const* fmt, va_list args) {
    TryLog(level::LogLevel::kDebug, tag, fmt, args);
}

void Logger::Info(char const* const tag, char const* fmt, va_list args) {
    TryLog(level::LogLevel::kInfo, tag, fmt, args);
}

void Logger::Warn(char const* const tag, char const* fmt, va_list args) {
    TryLog(level::LogLevel::kWarn, tag, fmt, args);
}

void Logger::Error(char const* const tag, char const* fmt, va_list args) {
    TryLog(level::LogLevel::kError, tag, fmt, args);
}

void Logger::Fatal(char const* const tag, char const* fmt, va_list args) {
    TryLog(level::LogLevel::kFatal, tag, fmt, args);
}

void Logger::SetLogLevel(level::LogLevel const lvl) { level_ = static_cast<int>(lvl); }

void Logger::PeriodicFlush() { sink_->Flush(); }

bool Logger::ShouldLog(level::LogLevel const lvl) const { return static_cast<int>(lvl) >= level_; }

void Logger::LogIt(details::LogMsg const& log_msg) { SinkIt(log_msg); }

void Logger::LogIt(level::LogLevel const lvl, std::string_view tag, std::string_view msg) {
    details::LogMsg const log_msg{lvl, tag, msg};
    LogIt(log_msg);
}

void Logger::SinkIt(details::LogMsg const& log_msg) {
    sink_->Log(log_msg);

    if (ShouldFlush(log_msg.level_)) {
        FlushIt();
    }
}

void Logger::FlushIt() { sink_->Flush(); }

bool Logger::ShouldFlush(level::LogLevel const lvl) const { return static_cast<int>(lvl) >= flush_level_; }

void Logger::TryLog(level::LogLevel const lvl, char const* const tag, char const* fmt, va_list args) {
    if (!ShouldLog(lvl)) {
        return;
    }

    std::array<char, kBufferSize> buf{};
    int const len = vsnprintf(buf.data(), buf.size(), fmt, args);
    if (len < 0) {
        syslog(LOG_ERR, "vsnprintf failed: errno: %d", errno);
        return;
    }

    details::LogMsg const log_msg(lvl, tag, buf.data());
    LogIt(log_msg);
}

}  // namespace ws::log
