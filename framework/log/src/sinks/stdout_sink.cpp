#include "sinks/stdout_sink.hpp"

namespace ws::log::sinks {

StdoutSink::StdoutSink(FILE* file) : target_file_{file}, formatter_{std::make_unique<Formatter>()} {}

void StdoutSink::Log(details::LogMsg const& log_msg) {
    std::lock_guard<std::mutex> const lock{mt_};
    FmtBuffer formatted;
    formatter_->Format(log_msg, formatted);
    std::size_t const buf_size = formatted.size();

    PrintColor(color::kColors.at(static_cast<int>(log_msg.level_)));
    if (::fwrite(formatted.data(), sizeof(char), buf_size, target_file_) != buf_size) {
        ::fprintf(stderr, "write to stdout failed, errno: %d\n", errno);
    }
    PrintColor(color::kReset);
}

void StdoutSink::Flush() {
    if (::fflush(target_file_) != 0) {
        ::fprintf(stderr, "flush to stdout failed, errno: %d\n", errno);
    }
}

void StdoutSink::PrintColor(std::string_view const& color_mode) {
    fwrite(color_mode.data(), sizeof(char), color_mode.size(), target_file_);
}

}  // namespace ws::log::sinks
