#include "sinks/rotating_file_sink.hpp"

#include <syslog.h>

#include <filesystem>

#include "details/time_helper.hpp"

namespace ws::log::sinks {

namespace {
constexpr char const* kLogNameSuffix{".log"};
}

RotatingFileSink::RotatingFileSink(std::string const& base_dir, std::string const& file_name,
                                   std::size_t const max_size)
    : base_dir_{base_dir},               // e.g.: "/log/app"
      log_dir_{base_dir + "log/"},       // e.g.: "/log/app/log"
      file_name_{file_name},             // e.g.: "example"
      file_path_{base_dir + file_name},  // e.g.: "/log/app/example"
      max_size_(max_size),
      file_helper_{file_path_ + kLogNameSuffix},
      formatter_{std::make_unique<Formatter>()} {
    curr_size_ = file_helper_.Size();
    if (curr_size_ > max_size_) {
        RotateFile();
        curr_size_ = 0;
    }
}

void RotatingFileSink::Log(details::LogMsg const& log_msg) {
    std::lock_guard<std::mutex> const lock{mt_};
    FmtBuffer formatted;
    formatter_->Format(log_msg, formatted);
    auto new_size = curr_size_ + formatted.size();

    if (new_size > max_size_) {
        file_helper_.Flush();
        if (file_helper_.Size() > 0) {
            RotateFile();
            new_size = formatted.size();
        }
    }

    file_helper_.Write(formatted);
    curr_size_ = new_size;
}

void RotatingFileSink::Flush() { file_helper_.Flush(); }

void RotatingFileSink::RotateFile() {
    try {
        auto const curr_time = details::time_helper::GetCurrDateTime();
        auto const target = MakeLogName(curr_time);

        std::filesystem::create_directories(log_dir_);
        std::filesystem::rename(file_helper_.FileName(), target);

        file_helper_.Close();
        file_helper_.Open();
    } catch (std::filesystem::filesystem_error const& e) {
        syslog(LOG_ERR, "rotate log file failed, err: %s", e.what());
    }
}

std::string RotatingFileSink::MakeLogName(std::string const& time) const {
    return log_dir_ + file_name_ + "-" + time + kLogNameSuffix;
}

}  // namespace ws::log::sinks
