#include "details/periodic_flusher.hpp"

#include <syslog.h>
namespace {
constexpr auto kPeriodicInterval = std::chrono::seconds(1);
}

namespace ws::log::details {

PeriodicFlusher::PeriodicFlusher(std::function<void()> const& periodic_flush) {
    worker_ = std::thread([this, periodic_flush]() {
        for (;;) {
            std::unique_lock<std::mutex> lock(mtx_);
            // 等待资源就绪或超时，如果谓词返回 true，线程立刻返回，函数返回 true
            if (cv_.wait_for(lock, kPeriodicInterval, [this]() { return !active_; })) {
                return;  // active_ == false, exit this thread.
            }
            periodic_flush();
        }
    });
}

PeriodicFlusher::~PeriodicFlusher() {
    if (worker_.joinable()) {
        {
            std::lock_guard<std::mutex> lock(mtx_);
            active_ = false;
        }
        cv_.notify_one();
        worker_.join();
    }
}

}  // namespace ws::log::details
