#include "details/async_thread_pool.hpp"

#include "async_logger.hpp"
#include "details/async_log_msg.hpp"

namespace ws::log::details {
AsyncThreadPool::AsyncThreadPool(std::size_t const worker_count, std::size_t const queue_size) : q_{queue_size} {
    for (std::size_t i = 0; i < worker_count; ++i) {
        threads_.emplace_back([this]() { this->WorkerLoop(); });
    }
}

AsyncThreadPool::~AsyncThreadPool() {
    AsyncLogMsg async_log_msg;
    while (q_.TryDequeue(async_log_msg)) {
        switch (async_log_msg.msg_type_) {
            case AsyncMsgType::kLog: {
                async_log_msg.worker_ptr_->BackendSinkIt(async_log_msg);
                break;
            }
            case AsyncMsgType::kFlush: {
                async_log_msg.worker_ptr_->BackendFlushIt();
                break;
            }
            default:
                break;
        }
    }

    for (size_t i = 0; i < threads_.size(); ++i) {
        PostAsyncMsg(AsyncLogMsg(AsyncMsgType::kTerminate), AsyncOverflowPolicy::kBlock);
    }

    for (auto& t : threads_) {
        t.join();
    }
}

void AsyncThreadPool::PostLog(AsyncLoggerPtr&& work_ptr, details::LogMsg const& log_msg,
                              AsyncOverflowPolicy overflow_policy) {
    AsyncLogMsg async_log_msg{std::move(work_ptr), AsyncMsgType::kLog, log_msg};
    PostAsyncMsg(std::move(async_log_msg), overflow_policy);
}

void AsyncThreadPool::PostFlush(AsyncLoggerPtr&& work_ptr, AsyncOverflowPolicy overflow_policy) {
    AsyncLogMsg async_log_msg{std::move(work_ptr), AsyncMsgType::kFlush};
    PostAsyncMsg(std::move(async_log_msg), overflow_policy);
}

void AsyncThreadPool::PostAsyncMsg(AsyncLogMsg&& async_log_msg, AsyncOverflowPolicy overflow_policy) {
    if (overflow_policy == AsyncOverflowPolicy::kOverrunOldest) {
        q_.EnqueueNoWait(std::move(async_log_msg));
    } else {
        q_.Enqueue(std::move(async_log_msg));
    }
}

void AsyncThreadPool::WorkerLoop() {
    while (FetchLogMsg()) {
    }
}

bool AsyncThreadPool::FetchLogMsg() {
    AsyncLogMsg async_log_msg;

    q_.Dequeue(async_log_msg);

    switch ((async_log_msg.msg_type_)) {
        case AsyncMsgType::kLog: {
            async_log_msg.worker_ptr_->BackendSinkIt(async_log_msg);
            return true;
        }
        case AsyncMsgType::kFlush: {
            async_log_msg.worker_ptr_->BackendFlushIt();
            return true;
        }
        case AsyncMsgType::kTerminate: {
            return false;
        }

        default:
            break;
    }

    return true;
}

}  // namespace ws::log::details
