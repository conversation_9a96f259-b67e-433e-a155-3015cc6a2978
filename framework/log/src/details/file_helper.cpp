#include "details/file_helper.hpp"

#include <fcntl.h>
#include <sys/stat.h>  // ::stat
#include <sys/types.h>
#include <syslog.h>
#include <unistd.h>  // ::fsync

#include <filesystem>

#include "fmt_defines.hpp"

namespace ws::log::details {

FileHelper::FileHelper(std::string file_path) : file_path_{std::move(file_path)} { Open(); }

FileHelper::~FileHelper() { Close(); }

void FileHelper::Open() {
    Close();

    CreateDir(DirName(file_path_));

    int32_t const fd =
        ::open(file_path_.c_str(), O_CREAT | O_WRONLY | O_APPEND | O_CLOEXEC, S_IRUSR | S_IRGRP | S_IROTH);
    if (fd == -1) {
        syslog(LOG_ERR, "open %s failed", file_path_.c_str());
        return;
    }

    char const* mode = "ab";
    target_file_ = fdopen(fd, mode);
    if (target_file_ == nullptr) {
        syslog(LOG_ERR, "fdopen %s failed", file_path_.c_str());
        ::close(fd);
        return;
    }
}

std::string FileHelper::DirName(std::string const& file_path) {
    std::filesystem::path const p{file_path};
    return p.parent_path();
}

bool FileHelper::CreateDir(std::string const& dir) {
    try {
        if (!std::filesystem::exists(dir)) {
            std::filesystem::create_directories(dir);
        }
        return true;
    } catch (std::filesystem::filesystem_error const& e) {
        syslog(LOG_ERR, "create dir %s failed %s", dir.c_str(), e.what());
        return false;
    }

    return false;
}

void FileHelper::Close() {
    if (target_file_ != nullptr) {
        Flush();
        if (::fclose(target_file_) != 0) {
            syslog(LOG_ERR, "close %s failed %d", file_path_.c_str(), errno);
        }
        target_file_ = nullptr;
    }
}

void FileHelper::Flush() {
    if (::fflush(target_file_) != 0) {
        syslog(LOG_ERR, "fflush %s failed %d", file_path_.c_str(), errno);
    }
}

void FileHelper::Write(FmtBuffer const& buf) {
    std::size_t const buf_size = buf.size();
    if (::fwrite(buf.data(), sizeof(char), buf_size, target_file_) != buf_size) {
        syslog(LOG_ERR, "fwrite %s failed %d", file_path_.c_str(), errno);
    }
}

std::size_t FileHelper::Size() const {
    struct stat64 st{};
    if (::fstat64(::fileno(target_file_), &st) != 0) {
        syslog(LOG_ERR, "fstat64 %s failed %d", file_path_.c_str(), errno);
        return 0;
    }

    return st.st_size;
}

std::string const& FileHelper::FileName() const { return file_path_; }

}  // namespace ws::log::details
