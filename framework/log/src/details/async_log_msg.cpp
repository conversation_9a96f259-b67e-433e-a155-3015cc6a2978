#include "details/async_log_msg.hpp"

namespace ws::log::details {

AsyncLogMsg::AsyncLogMsg(std::shared_ptr<AsyncLogger>&& worker, AsyncMsgType msg_type, LogMsg const& log_msg)
    : LogMsgBuffer{log_msg}, msg_type_{msg_type}, worker_ptr_{std::move(worker)} {}

AsyncLogMsg::AsyncLogMsg(std::shared_ptr<AsyncLogger>&& worker, AsyncMsgType msg_type)
    : LogMsgBuffer{}, msg_type_{msg_type}, worker_ptr_{std::move(worker)} {}

AsyncLogMsg::AsyncLogMsg(AsyncMsgType msg_type) : AsyncLogMsg{nullptr, msg_type} {}

}  // namespace ws::log::details
