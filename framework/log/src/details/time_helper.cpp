#include "details/time_helper.hpp"

#include <array>
#include <cstdint>
#include <ctime>  // std::time_t/std::tm

namespace ws::log::details::time_helper {

std::string GetCurrDateTime() {
    std::time_t const t = std::time(nullptr);
    std::tm now{};
    ::localtime_r(&t, &now);
    static constexpr uint64_t kMaxDateStrLen{64UL};
    std::array<char, kMaxDateStrLen> data_str{};
    std::size_t const len =
        snprintf(data_str.data(), data_str.size(), "%04d_%02d_%02d_%02d_%02d_%02d", (now.tm_year + 1900),
                 (now.tm_mon + 1), now.tm_mday, now.tm_hour, now.tm_min, now.tm_sec);
    return {data_str.data(), len};
}

}  // namespace ws::log::details::time_helper
