#include "details/log_msg.hpp"

#include <sys/syscall.h>  // syscall()
#include <unistd.h>

namespace ws::log::details {

LogMsg::LogMsg(level::LogLevel const lvl, std::string_view tag, std::string_view msg)
    : time_{std::chrono::system_clock::now()},
      level_{lvl},
      tag_{tag},
      msg_{msg},
      pid_{GetPid()},
      thread_id_{GetThreadId()} {}

std::size_t LogMsg::GetPid() noexcept {
    static std::size_t const kPid = static_cast<std::size_t>(::syscall(SYS_getpid));
    return kPid;
}

std::size_t LogMsg::GetThreadId() noexcept {
    static thread_local std::size_t const kTid = static_cast<std::size_t>(::syscall(SYS_gettid));
    return kTid;
}

}  // namespace ws::log::details
