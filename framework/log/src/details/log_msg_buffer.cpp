#include "details/log_msg_buffer.hpp"

namespace ws::log::details {

LogMsgBuffer::LogMsgBuffer(LogMsg const& orig_msg) : LogMsg{orig_msg} {
    buffer_.append(tag_.begin(), tag_.end());
    buffer_.append(msg_.begin(), msg_.end());
    UpdateStringViews();
}

LogMsgBuffer::LogMsgBuffer(LogMsgBuffer&& other) noexcept : LogMsg{other}, buffer_{std::move(other.buffer_)} {
    UpdateStringViews();
}

LogMsgBuffer& LogMsgBuffer::operator=(LogMsgBuffer&& other) noexcept {
    LogMsg::operator=(other);
    buffer_ = std::move(other.buffer_);
    UpdateStringViews();
    return *this;
}

void LogMsgBuffer::UpdateStringViews() {
    tag_ = std::string_view{buffer_.data(), tag_.size()};
    msg_ = std::string_view{buffer_.data() + tag_.size(), msg_.size()};
}

}  // namespace ws::log::details
