#include "log/log.hpp"

#include <chrono>
#include <cstdarg>  // va_list
#include <cstring>  // strerror

#include "log/register.hpp"

namespace ws::log {

Logger* DefaultLoggerRaw() { return Register::Instance().GetDefaultRaw(); }

#ifdef BUILD_TESTING
void SetLogLevel(level::LogLevel const lvl) { Register::Instance().SetLogLevel(lvl); }

void SetDefaultLogger(std::shared_ptr<Logger> new_default_logger) {
    Register::Instance().SetDefaultLogger(std::move(new_default_logger));
}
#endif  // BUILD_TESTING

void Debug(char const* const tag, char const* const fmt, ...) noexcept {
    va_list args;
    va_start(args, fmt);
    DefaultLoggerRaw()->Debug(tag, fmt, args);
    va_end(args);
}

void Info(char const* const tag, char const* const fmt, ...) noexcept {
    va_list args;
    va_start(args, fmt);
    DefaultLoggerRaw()->Info(tag, fmt, args);
    va_end(args);
}

void Warn(char const* const tag, char const* const fmt, ...) noexcept {
    va_list args;
    va_start(args, fmt);
    DefaultLoggerRaw()->Warn(tag, fmt, args);
    va_end(args);
}

void Error(char const* const tag, char const* const fmt, ...) noexcept {
    va_list args;
    va_start(args, fmt);
    DefaultLoggerRaw()->Error(tag, fmt, args);
    va_end(args);
}

void Fatal(char const* const tag, char const* const fmt, ...) noexcept {
    va_list args;
    va_start(args, fmt);
    DefaultLoggerRaw()->Fatal(tag, fmt, args);
    va_end(args);
}

}  // namespace ws::log
