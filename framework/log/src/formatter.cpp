#include "formatter.hpp"

#include <chrono>  // to_time_t
#include <cstring>
#include <ctime>  // localtime_r

#include "details/fmt_helper.hpp"

namespace {
constexpr int kTen = 10;
constexpr int kHundred = 100;
constexpr int kThousand = 1000;
}  // namespace

namespace ws::log {
namespace level {

static constexpr std::array<char const*, 5> kLevels{"D", "I", "W", "E", "F"};
constexpr const char* GetLevel(LogLevel const l) noexcept { return kLevels[static_cast<std::size_t>(l)]; }

}  // namespace level

Formatter::Formatter(TimeZoneType time_zone_type) : time_zone_type_{time_zone_type}, last_log_secs_{0} {}

void Formatter::Format(details::LogMsg const& log_msg, FmtBuffer& dest) {
    auto const secs = std::chrono::duration_cast<std::chrono::seconds>(log_msg.time_.time_since_epoch());

    if (secs != last_log_secs_ || datetime_cache_.size() == 0) {
        cache_tm_ = GetTime(log_msg);
        last_log_secs_ = secs;

        datetime_cache_.clear();

        FormatPad(cache_tm_.tm_mon + 1);
        datetime_cache_.push_back('-');

        FormatPad(cache_tm_.tm_mday);
        datetime_cache_.push_back(' ');

        FormatPad(cache_tm_.tm_hour);
        datetime_cache_.push_back(':');

        FormatPad(cache_tm_.tm_min);
        datetime_cache_.push_back(':');

        FormatPad(cache_tm_.tm_sec);
        datetime_cache_.push_back('.');
    }
    dest.append(datetime_cache_.begin(), datetime_cache_.end());

    auto const millis_part = details::fmt_helper::TimeFraction<std::chrono::milliseconds>(log_msg.time_);
    FormatMillis(static_cast<int>(millis_part.count()), dest);

    fmt::format_to(std::back_inserter(dest), "{}", log_msg.pid_);
    dest.push_back(' ');

    fmt::format_to(std::back_inserter(dest), "{}", log_msg.thread_id_);
    dest.push_back(' ');

    char const* lvl = level::GetLevel(log_msg.level_);
    details::fmt_helper::AppendStringView(lvl, dest);
    dest.push_back(' ');

    details::fmt_helper::AppendStringView(log_msg.tag_, dest);
    details::fmt_helper::AppendStringView(": ", dest);

    details::fmt_helper::AppendStringView(log_msg.msg_, dest);

    if (log_msg.msg_.back() != kDefaultEol) {
        dest.push_back(kDefaultEol);
    }
}

void Formatter::FormatPad(int const n) {
    datetime_cache_.push_back(static_cast<char>('0' + (n / kTen)));
    datetime_cache_.push_back(static_cast<char>('0' + (n % kTen)));
}

void Formatter::FormatMillis(int n, fmt::basic_memory_buffer<char, kFmtBufferSize>& dest) {
    if (n < kThousand) {
        dest.push_back(static_cast<char>('0' + (n / kHundred)));
        n = n % kHundred;
        dest.push_back(static_cast<char>('0' + (n / kTen)));
        dest.push_back(static_cast<char>('0' + (n % kTen)));
    } else {
        fmt::format_int const i(n);
        dest.append(i.data(), i.data() + i.size());
    }
}

std::tm Formatter::GetTime(details::LogMsg const& log_msg) {
    if (time_zone_type_ == TimeZoneType::kLocal) {
        return LocalTime(std::chrono::system_clock::to_time_t(log_msg.time_));
    }

    return GmTime(std::chrono::system_clock::to_time_t(log_msg.time_));
}

std::tm Formatter::LocalTime(std::time_t const& time_tt) {
    std::tm tm{};
    ::localtime_r(&time_tt, &tm);
    return tm;
}

std::tm Formatter::GmTime(std::time_t const& time_tt) {
    std::tm tm{};
    ::gmtime_r(&time_tt, &tm);
    return tm;
}

}  // namespace ws::log
