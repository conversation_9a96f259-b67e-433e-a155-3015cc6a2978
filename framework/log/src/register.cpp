#include "log/register.hpp"

#include "register_impl.hpp"

namespace {
// 全局单例
ws::log::Register const *const kRegister = &(ws::log::Register::Instance());
}  // namespace

namespace ws::log {
Register &Register::Instance() {
    static Register instance;
    return instance;
}

Register::Register() : pimpl_{std::make_unique<Register::RegisterImpl>()} {}

Register::~Register() = default;

Logger *Register::GetDefaultRaw() { return pimpl_->GetDefaultRaw(); }

#ifdef BUILD_TESTING
void Register::SetLogLevel(level::LogLevel const lvl) { pimpl_->SetLogLevel(lvl); }

void Register::SetDefaultLogger(std::shared_ptr<Logger> new_default_logger) {
    pimpl_->SetDefaultLogger(std::move(new_default_logger));
}
#endif  // BUILD_TESTING

}  // namespace ws::log
