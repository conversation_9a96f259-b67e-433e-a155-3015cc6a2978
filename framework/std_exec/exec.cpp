#include <sys/errno.h>

#include <condition_variable>
#include <cstdio>
#include <exception>
#include <functional>
#include <iostream>
#include <mutex>
#include <optional>
#include <thread>
#include <utility>

// Some utility code
///////////////////////////////////////////
struct immovable {
    immovable() = default;
    immovable(immovable&&) = delete;
};

// alias
template <typename S, typename R>
using connect_result_t = decltype(connect(std::declval<S>(), std::declval<R>()));  // connect()函数返回的类型

///////////////////////////////////////////
// just(T) sender factory
///////////////////////////////////////////

template <typename R, typename T>
struct just_operation : immovable {
    R rec;    // 表示receiver
    T value;  // 表示receiver对应的值类型是T

    template <typename R>
    friend void start(just_operation& self) {
        // start()函数必须立马完成 => 这意味着它只需要调用receiver上的一个set function。
        set_value(self.rec, self.value);
    }
};

template <typename T>
struct just_sender {
    T value;

    // 所有的sender都需要实现connect，connect函数接收一个receiver，并返回一个operation state.
    // 它返回一个receiver(R)，并且这个值类型是T.
    template <typename R>
    friend just_operfation<R, T> connect(just_sender, self, R rec) {
        return {{}, rec, self.value};
    }
};

template <typename T>
just_sender<T> just(T t) {
    return {t};
}

///////////////////////////////////////////
// then(Sender, Function) sender adaptor
///////////////////////////////////////////

template <class R, class F>
struct then_receiver {
    R rec;
    F f;

    friend void set_value(then_receiver self, auto val) { set_value(self.rec, self.f(val)); }

    friend void set_error(then_receiver self, std::exception_ptr err) { set_error(self.rec, err); }

    friend void set_stopped(then_receiver self) { set_stopped(self.rec); }
};

// 将工作连接到其他工作上 chain work on the other work => then()

template <typename S, typename R, typename F>
struct then_operation : immovable {
    connect_result_t<R, then_receiver<R, F>> op;

    friend void start(then_operation& self) { start(self.op); }
};

// 对于then_send()来说，它将从connect()函数中返回一个then_operation
template <typename S, typename F>
struct then_sender {
    S s;
    F f;

    // 需要返回一个
    template <typename R>
    friend then_operation<S, R, F> connect(then_sender self, R rec) {
        return {{}, connect(self.s, then_receiver<R, F>{rec, self.f})};
    }
};

// then()函数需要一个sender和一个function
template <typename S, typename F>
then_sender<S, F> then(S s, F f) {
    return {s, f};
}

//
// start test code
//
struct cout_receiver {
    friend void set_value(cout_receiver self, auto val) { std::cout << "Result: " << val << '\n'; }

    friend void set_error(cout_receiver self, std::exception_ptr err) { std::terminate(); }

    friend void set_stopped(cout_receiver self) { std::terminate(); }
};

int main() {
    auto s = just(42);
    auto op = connect(s, cout_receiver{});
    start(op);

    // auto s2 = then(just(42), [](int i) { return i + 1; });
    // auto op2 = connect(s2, cout_receiver{});
    // start(op2);

    // auto s3 = then(just(42), [](int i) { return i + 1; });
    // int val3 = sync_wait(s3).value();
    // std::cout << "Result: " << val3 << '\n';

    // run_loop loop;
    // auto sched4 = loop.get_scheduler();
    // auto s4 = then(schedule(sched4), [](auto) { return 42; });
    // auto op4 = connect(s4, cout_receiver{});
    // start(op4);
    // auto s5 = then(schedule(sched4), [](auto) { return 43; });
    // auto op5 = connect(s5, cout_receiver{});
    // start(op5);

    // loop.finish();
    // loop.run();

    // thread_context th;
    // auto sched6 = th.get_scheduler();

    // auto s6 = then(schedule(sched6), [](auto) { return 42; });
    // auto s7 = then(s6, [](int i) { return i + 1; });
    // auto val7 = sync_wait(s7).value();
    // th.finish();
    // th.join();

    // std::cout << val7 << '\n';
}