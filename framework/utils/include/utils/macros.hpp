#ifndef UTILS_MACRO_HPP_
#define UTILS_MACRO_HPP_

// disable class copy and move
#define DISABLE_COPY_AND_MOVE(ClassName)             \
    ClassName(const ClassName&) = delete;            \
    ClassName& operator=(const ClassName&) = delete; \
    ClassName(ClassName&&) = delete;                 \
    ClassName& operator=(ClassName&&) = delete;

// disable class copy
#define DISABLE_COPY(ClassName)                      \
    ClassName(const ClassName&) = delete;            \
    ClassName& operator=(const ClassName&) = delete; \
    ClassName(ClassName&&) = default;                \
    ClassName& operator=(ClassName&&) = default;

// disable class move
#define DISABLE_MOVE(ClassName)                       \
    ClassName(const ClassName&) = default;            \
    ClassName& operator=(const ClassName&) = default; \
    ClassName(ClassName&&) = delete;                  \
    ClassName& operator=(ClassName&&) = delete;

#ifndef UNUSED
#define UNUSED(x) static_cast<void>(x)
#endif

#endif  // UTILS_MACRO_HPP_