#ifndef UTILS_MUTEX_HELPER_HPP_
#define UTILS_MUTEX_HELPER_HPP_

#include <mutex>
#include <shared_mutex>

namespace ws::utils {

template <typename T>
class MutexHelper {
public:
    virtual ~MutexHelper() = default;
    std::mutex mutex;
    T data;
};

template <typename T>
class SharedMutexHelper {
public:
    virtual ~SharedMutexHelper() = default;
    std::shared_mutex mutex;
    T data;
};

}  // namespace ws::utils

#endif  // UTILS_MUTEX_HELPER_HPP_
