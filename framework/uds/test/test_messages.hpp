#ifndef UDS_TEST_MESSAGES_HPP_
#define UDS_TEST_MESSAGES_HPP_

#include <string>
#include "type/serializable.hpp"

namespace ws::uds::test {

// 简单的请求消息
struct SimpleRequest : public type::Serializable {
    int32_t id;
    int32_t command;
    std::string data;
    
    SERIALIZABLE(id, command, data);
};

// 简单的响应消息
struct SimpleResponse : public type::Serializable {
    int32_t id;
    int32_t status;
    std::string data;
    
    SERIALIZABLE(id, status, data);
};

// 大型消息，用于测试消息大小限制
struct LargeRequest : public type::Serializable {
    int32_t id;
    std::string large_data; // 将填充大量数据
    
    SERIALIZABLE(id, large_data);
};

// 用于测试的命令类型
enum class TestCommand : int32_t {
    ECHO = 1,           // 简单回显
    DELAY = 2,          // 延迟响应
    CRASH_SERVER = 3,   // 模拟服务器崩溃
    LARGE_RESPONSE = 4, // 返回大型响应
    INVALID = 5         // 无效命令
};

} // namespace ws::uds::test

#endif // UDS_TEST_MESSAGES_HPP_
