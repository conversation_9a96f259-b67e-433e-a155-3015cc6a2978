#include "uds_server_impl.hpp"

#include <fcntl.h>
#include <fmt/format.h>  // fmt::format()
#include <poll.h>        // poll()
#include <sys/socket.h>
#include <sys/uio.h>  // struct iovec
#include <sys/un.h>
#include <unistd.h>

#include <algorithm>
#include <string_view>

#include "details/socket_dir.hpp"
#include "log/log.hpp"
#include "uds/uds_define.hpp"
#include "uds/uds_server.hpp"

namespace {
/// @brief shut down control pipe.
constexpr char kCtrlPipeShutdown = 0;
}  // namespace

namespace ws::uds {

UdsServerTypeless::UdsServerTypeless(std::string const& uds_name, bool const enable_auth) {
    pimpl_ = std::make_unique<UdsServerImpl>(uds_name, enable_auth);
}

UdsServerTypeless::~UdsServerTypeless() = default;

void UdsServerTypeless::AsyncResponses(UdsServerTypelessCallback&& callback) {
    pimpl_->AsyncResponses(std::move(callback));
}

bool UdsServerTypeless::StopListener() { return pimpl_->StopListener(); }

void UdsServerTypeless::AddUidAuthWhiteList(uid_t const uid) { pimpl_->AddUidAuthWhiteList(uid); }

void UdsServerTypeless::AddGidAuthWhiteList(gid_t const gid) { pimpl_->AddGidAuthWhiteList(gid); }

void UdsServerTypeless::AddAuthWhiteList(uid_t const uid, gid_t const gid) { pimpl_->AddAuthWhiteList(uid, gid); }

//////////// uds server impl /////////////////////
UdsServerImpl::UdsServerImpl(std::string uds_name, bool const enable_auth)
    : uds_name_{std::move(uds_name)}, enable_auth_{enable_auth} {
    sock_fd_ = TEMP_FAILURE_RETRY(::socket(AF_UNIX, SOCK_SEQPACKET, 0));
    if (sock_fd_ < 0) {
        log::ErrorFmt(kLogTag, "create socket fail. error: {}", strerror(errno));
        return;
    }

    Bind();

    int i = 1;
    setsockopt(sock_fd_, SOL_SOCKET, SO_REUSEADDR, &i, sizeof(i));

    Listen();

    if (pipe2(ctrl_pipe_.data(), O_CLOEXEC) == -1) {
        log::ErrorFmt(kLogTag, "create control pipe fail. error: {}", strerror(errno));
        return;
    }

    worker_thread_ = std::thread([this] { RunListener(); });
}

UdsServerImpl::~UdsServerImpl() {
    log::WarnFmt(kLogTag, "UdsWrapperServer destructor.");

    running_.store(false);

    if (sock_fd_ != -1) {
        ::close(sock_fd_);
    }

    if (ctrl_pipe_[0] != -1) {
        ::close(ctrl_pipe_[0]);
        ::close(ctrl_pipe_[1]);
    }

    {
        std::lock_guard<std::mutex> lock(client_fds_.mutex);
        for (auto& [fd, client] : client_fds_.data) {
            ::close(fd);
        }

        client_fds_.data.clear();
    }

    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }
}

bool UdsServerImpl::StopListener() {
    char c = kCtrlPipeShutdown;
    ssize_t const rc{TEMP_FAILURE_RETRY(::write(ctrl_pipe_[1], &c, 1))};
    if (rc != 1) {
        log::ErrorFmt(kLogTag, "write control pipe fail. error: {}", strerror(errno));
        return false;
    }

    if (sock_fd_ != -1) {
        ::close(sock_fd_);
        sock_fd_ = -1;
    }

    ::close(ctrl_pipe_[0]);
    ::close(ctrl_pipe_[1]);
    ctrl_pipe_[0] = -1;
    ctrl_pipe_[1] = -1;

    {
        std::lock_guard<std::mutex> lock(client_fds_.mutex);
        for (auto& [fd, client] : client_fds_.data) {
            ::close(fd);
        }
        client_fds_.data.clear();
    }

    return true;
}

void UdsServerImpl::AsyncResponses(UdsServerImplCallback&& callback) { callback_ = std::move(callback); }

bool UdsServerImpl::Bind() {
    struct sockaddr_un addr{};
    addr.sun_family = AF_UNIX;

    std::string const socket_path = fmt::format("{}/{}", kSocketDir, uds_name_);  // "/dev/socket/XXX"
    if (socket_path.size() >= sizeof(addr.sun_path) || uds_name_.find('/') != std::string::npos) {
        log::ErrorFmt(kLogTag, "socket path too long or contains illegal character: {}", socket_path);
        throw std::runtime_error("socket path too long or contains illegal character");
    }
    strncpy(addr.sun_path, socket_path.c_str(), sizeof(addr.sun_path) - 1);

    if (!CreateSocketDir(kSocketDir)) {
        log::ErrorFmt(kLogTag, "create socket dir fail. dir: {}", kSocketDir);
        return false;
    }

    // server clear up previous socket if it exists.
    unlink(socket_path.c_str());

    socklen_t const addr_len = std::strlen(addr.sun_path) + offsetof(struct sockaddr_un, sun_path) + 1;

    if (TEMP_FAILURE_RETRY(::bind(sock_fd_, (struct sockaddr*)&addr, addr_len)) == -1) {
        log::ErrorFmt(kLogTag, "bind socket fail. error: {}", strerror(errno));
        ::close(sock_fd_);
        sock_fd_ = -1;
        return false;
    }

    static constexpr mode_t kFileMode = 0777;
    if (::chmod(socket_path.c_str(), kFileMode) == -1) {
        log::ErrorFmt(kLogTag, "chmod socket fail. error: {}", strerror(errno));
        return false;
    }

    return true;
}

bool UdsServerImpl::Listen() {
    static constexpr int32_t kBackLog{10};

    log::DebugFmt(kLogTag, "listen sock fd: {}", sock_fd_);
    if (::listen(sock_fd_, kBackLog) == -1) {
        log::ErrorFmt(kLogTag, "listen socket fail. error: {}", strerror(errno));
        ::close(sock_fd_);
        sock_fd_ = -1;
        return false;
    }

    return true;
}

bool UdsServerImpl::AcceptClients() {
    int32_t const client_fd = TEMP_FAILURE_RETRY(::accept4(sock_fd_, nullptr, nullptr, SOCK_CLOEXEC));
    if (client_fd < 0) {
        log::ErrorFmt(kLogTag, "accept client fail. error: {}", strerror(errno));
        return false;
    }

    auto new_client = std::make_unique<SocketClient>(client_fd);

    // verify client permission.
    if (enable_auth_ && !authenticator_.VerifyClient(new_client.get())) {
        log::ErrorFmt(kLogTag, "client is not authorized, rejecting connection");
        ::close(client_fd);
        return false;
    }

    // add new connect client to client_fds.
    {
        std::lock_guard<std::mutex> lock(client_fds_.mutex);
        client_fds_.data[client_fd] = std::move(new_client);
    }

    log::WarnFmt(kLogTag, "a new client is connected, accepted client fd: {}", client_fd);
    return true;
}

void UdsServerImpl::RunListener() {
    while (running_.load()) {
        std::vector<pollfd> fds;

        {
            std::lock_guard<std::mutex> lock(client_fds_.mutex);
            fds.reserve(client_fds_.data.size() + 2);  // +2 for the server socket and control pipe.

            // add control pipe to the poll list.
            fds.push_back({.fd = ctrl_pipe_[0], .events = POLLIN});

            // add listener socket to the poll list.
            fds.push_back({.fd = sock_fd_, .events = POLLIN});

            for (auto const& [fd, client] : client_fds_.data) {
                fds.push_back({.fd = fd, .events = POLLIN});
            }
        }

        // listen events on the server socket and all connected client sockets.
        int32_t const poll_ret{TEMP_FAILURE_RETRY(::poll(fds.data(), fds.size(), -1))};
        if (poll_ret < 0) {
            log::ErrorFmt(kLogTag, "poll fail. error: {}", strerror(errno));
            continue;
        }

        // close the control pipe if the server is stop listening.
        if (fds[0].revents & (POLLIN | POLLERR)) {
            log::WarnFmt(kLogTag, "event is generated in the read control pipe");
            char c = kCtrlPipeShutdown;
            TEMP_FAILURE_RETRY(::read(ctrl_pipe_[0], &c, 1));
            if (c == kCtrlPipeShutdown) {
                log::WarnFmt(kLogTag, "server is stop listening");
                break;
            }
            continue;
        }

        // a new client is connecting.
        if (fds[1].revents & POLLIN) {
            if (!AcceptClients()) {
                continue;
            }
        }

        // handle events on connected client sockets.
        int32_t const size = fds.size();
        for (int32_t i = 2; i < size; ++i) {
            auto const revents = fds[i].revents;
            int32_t const client_fd = fds[i].fd;

            // handle client disconnection. remove client from client_fds.data.
            if (revents & (POLLHUP | POLLERR)) {
                ::close(client_fd);
                {
                    std::lock_guard<std::mutex> lock(client_fds_.mutex);
                    client_fds_.data.erase(client_fd);
                }
                log::WarnFmt(kLogTag, "client disconnected, fd: {}", client_fd);
                continue;
            }

            // handle requests from connected clients.
            if (fds[i].revents & POLLIN) {
                SocketClient* client{nullptr};
                {
                    std::lock_guard<std::mutex> lock(client_fds_.mutex);
                    auto it = client_fds_.data.find(client_fd);
                    if (it != client_fds_.data.end()) {
                        client = (it->second).get();
                    }
                }

                if (client) {
                    OnDataAvailable(client);
                }
            }
        }
    }
}

bool UdsServerImpl::OnDataAvailable(SocketClient const* const client) const {
    type::Buffer request_buffer;
    type::Buffer response_buffer;

    // receive client request.
    if (!ReceiveRequest(client, request_buffer)) {
        log::ErrorFmt(kLogTag, "receive request fail");
        return false;
    }

    if (callback_ == nullptr) {
        log::ErrorFmt(kLogTag, "callback is nullptr");
        return false;
    }

    // call the callback to handle the request and generate a response.
    int32_t const status = callback_(request_buffer, response_buffer);
    log::DebugFmt(kLogTag, "callback status: {}", status);

    // send response to client.
    if (!SendResponse(client, response_buffer)) {
        log::ErrorFmt(kLogTag, "send response fail");
        return false;
    }

    return true;
}

bool UdsServerImpl::ReceiveRequest(SocketClient const* const client, type::Buffer& request_buffer) {
    uint32_t request_data_size = 0;

    std::array<struct iovec, 2UL> iov{};
    iov[0].iov_base = &request_data_size;
    iov[0].iov_len = sizeof(request_data_size);

    struct msghdr peek_msg = {
        .msg_iov = iov.data(),
        .msg_iovlen = 1,
    };

    ssize_t const peeked_ret{::recvmsg(client->GetSocket(), &peek_msg, MSG_PEEK)};
    if (peeked_ret < 0) {
        log::ErrorFmt(kLogTag, "first recvmsg error: {}", strerror(errno));
        return false;
    }

    // // 后续判断是否有必要检查消息大小，或者先读通过getsockopt读取socket
    // buffer中允许的最大消息大小，然后再检查消息大小。 static constexpr uint32_t kMaxMessageSize = 1024 * 1024;  // 1MB
    // if (request_data_size > kMaxMessageSize) {
    //     log::ErrorFmt(kLogTag, "Request message too large: {} bytes (max: {} bytes)", request_data_size,
    //                   kMaxMessageSize);
    //     return false;
    // }

    // if (request_data_size == 0) {
    //     log::WarnFmt(kLogTag, "Received empty request message");
    //     request_buffer.clear();
    //     return true;
    // }

    request_buffer.resize(request_data_size);

    iov[1].iov_base = request_buffer.data();
    iov[1].iov_len = request_buffer.size();

    struct msghdr msg = {
        .msg_iov = iov.data(),
        .msg_iovlen = 2,
    };

    ssize_t const ret{::recvmsg(client->GetSocket(), &msg, 0)};
    if (ret < 0) {
        log::ErrorFmt(kLogTag, "recvmsg fail. error: {}", strerror(errno));
        return false;
    }

    return true;
}

bool UdsServerImpl::SendResponse(SocketClient const* const client, type::Buffer const& response_buffer) {
    int32_t const response_buffer_size = response_buffer.size();

    // log::DebugFmt(kLogTag, "服务端开始发送响应，大小: {} bytes", response_buffer_size);

    std::array<struct iovec, 2UL> iov{};
    iov[0].iov_base = const_cast<void*>(static_cast<const void*>(&response_buffer_size));
    iov[0].iov_len = sizeof(response_buffer_size);
    iov[1].iov_base = const_cast<void*>(static_cast<const void*>(response_buffer.data()));
    iov[1].iov_len = response_buffer.size();

    ssize_t const ret{TEMP_FAILURE_RETRY(::writev(client->GetSocket(), iov.data(), 2))};
    if (ret < 0) {
        log::ErrorFmt(kLogTag, "writev fail. error: {}", strerror(errno));
        return false;
    }

    // log::DebugFmt(kLogTag, "服务端发送响应完成，实际发送: {} bytes", ret);
    return true;
}
}  // namespace ws::uds