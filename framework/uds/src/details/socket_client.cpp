#include "details/socket_client.hpp"

#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

#include "log/log.hpp"

namespace ws::uds {
SocketClient::SocketClient(int32_t const sock_fd) : sock_fd_(sock_fd) { GetClientCredentials(); }

bool SocketClient::GetClientCredentials() {
    struct ucred cred{};
    socklen_t len = sizeof(cred);

    if (::getsockopt(sock_fd_, SOL_SOCKET, SO_PEERCRED, &cred, &len) == 0) {
        pid_ = cred.pid;
        uid_ = cred.uid;
        gid_ = cred.gid;
        log::DebugFmt(kLogTag, "get client credentials: uid: {}, gid: {}, pid: {}", uid_, gid_, pid_);
    }

    return true;
}

}  // namespace ws::uds