#include "details/authenticator.hpp"

#include "log/log.hpp"

namespace {
/// @brief Traditional linux root user.
constexpr int32_t kRoot = 0;
}  // namespace

namespace ws::uds {
void Authenticator::AddUidAuthWhiteList(uid_t const uid) {
    std::lock_guard<std::mutex> lock(allowed_uids_.mutex);
    allowed_uids_.data.insert(uid);
}

void Authenticator::AddGidAuthWhiteList(gid_t const gid) {
    std::lock_guard<std::mutex> lock(allowed_gids_.mutex);
    allowed_gids_.data.insert(gid);
}

void Authenticator::AddAuthWhiteList(uid_t const uid, gid_t const gid) {
    AddUidAuthWhiteList(uid);
    AddGidAuthWhiteList(gid);
}

bool Authenticator::VerifyClient(SocketClient const* const client) {
    bool uid_allowed = false;
    bool gid_allowed = false;

    {
        std::lock_guard<std::mutex> lock(allowed_uids_.mutex);
        uid_allowed =
            client->GetGid() == kRoot || allowed_uids_.data.find(client->GetUid()) != allowed_uids_.data.end();
    }

    {
        std::lock_guard<std::mutex> lock(allowed_gids_.mutex);
        gid_allowed =
            client->GetGid() == kRoot || allowed_gids_.data.find(client->GetGid()) != allowed_gids_.data.end();
    }

    log::DebugFmt(kLogTag, "client verification, uid_allowed: {}, gid_allowed: {}", uid_allowed, gid_allowed);

    if (uid_allowed || gid_allowed) {
        log::DebugFmt(kLogTag, "client verification successed");
        return true;
    }

    log::WarnFmt(kLogTag, "client verification failed, uid:{}, gid:{}, pid: {}", client->GetUid(), client->GetGid(),
                 client->GetPid());

    return false;
}

}  // namespace ws::uds