#ifndef UDS_SERVER_IMPL_HPP_
#define UDS_SERVER_IMPL_HPP_

#include <sys/socket.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/un.h>

#include <atomic>
#include <string>
#include <thread>
#include <vector>

#include "details/authenticator.hpp"
#include "details/socket_client.hpp"
#include "uds/uds_define.hpp"
#include "utils/macros.hpp"
#include "utils/mutex_helper.hpp"

namespace ws::uds {

class UdsServerImpl {
    using UdsServerImplCallback = std::function<int32_t(type::Buffer const&, type::Buffer&)>;

public:
    explicit UdsServerImpl(std::string uds_name, bool const enable_auth);
    ~UdsServerImpl();
    DISABLE_COPY_AND_MOVE(UdsServerImpl)

    bool StopListener();

    void AsyncResponses(UdsServerImplCallback&& callback);

    [[nodiscard]]
    std::string GetUdsPath() const {
        return uds_name_;
    }

    void AddUidAuthWhiteList(uid_t const uid) { return authenticator_.AddUidAuthWhiteList(uid); }
    void AddGidAuthWhiteList(gid_t const gid) { return authenticator_.AddGidAuthWhiteList(gid); }
    void AddAuthWhiteList(uid_t const uid, gid_t const gid) { return authenticator_.AddAuthWhiteList(uid, gid); }

private:
    bool Bind();

    bool Listen();

    bool AcceptClients();

    void RunListener();

    bool OnDataAvailable(SocketClient const* const client) const;
    static bool ReceiveRequest(SocketClient const* const client, type::Buffer& request_buffer);
    static bool SendResponse(SocketClient const* const client, type::Buffer const& response_buffer);

    std::string uds_name_{};
    bool enable_auth_{true};
    int32_t sock_fd_{-1};
    std::atomic<bool> running_{true};
    Authenticator authenticator_;

    /// @brief Conrtol pipe for worker thread. Used to stop the listener thread.
    static constexpr int32_t kPipeSize = 2;             // size of control pipe.
    std::array<int32_t, kPipeSize> ctrl_pipe_{-1, -1};  // control pipe for worker thread.

    /// @brief Managing connected clients.
    using SocketClientPtr = std::unique_ptr<SocketClient>;
    utils::MutexHelper<std::unordered_map<int32_t, SocketClientPtr>> client_fds_;
    UdsServerImplCallback callback_;
    std::thread worker_thread_;

    static constexpr char const* kLogTag{"uds-server-impl"};
};

}  // namespace ws::uds

#endif  // UDS_SERVER_IMPL_HPP_