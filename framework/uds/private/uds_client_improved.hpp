#ifndef UDS_CLIENT_IMPROVED_HPP_
#define UDS_CLIENT_IMPROVED_HPP_

#include <chrono>
#include <mutex>
#include <string>
#include <system_error>
#include <optional>
#include <atomic>

#include "uds/uds_define.hpp"
#include "utils/macros.hpp"

namespace ws::uds {

// 客户端连接状态
enum class ConnectionState : uint8_t {
    kDisconnected = 0,
    kConnecting = 1,
    kConnected = 2,
    kError = 3
};

// 客户端配置
struct ClientConfig {
    std::chrono::nanoseconds default_timeout{std::chrono::seconds(30)};
    uint32_t max_reconnect_attempts{3};
    std::chrono::milliseconds reconnect_delay{std::chrono::milliseconds(100)};
    uint32_t max_message_size{1024 * 1024};  // 1MB
    bool enable_statistics{true};
};

// 统计信息
struct ClientStatistics {
    std::atomic<uint64_t> total_requests{0};
    std::atomic<uint64_t> successful_requests{0};
    std::atomic<uint64_t> failed_requests{0};
    std::atomic<uint64_t> timeout_requests{0};
    std::atomic<uint64_t> reconnect_count{0};
    std::atomic<uint64_t> bytes_sent{0};
    std::atomic<uint64_t> bytes_received{0};
    
    // 获取成功率
    [[nodiscard]] double GetSuccessRate() const noexcept {
        auto total = total_requests.load();
        return total > 0 ? static_cast<double>(successful_requests.load()) / total : 0.0;
    }
};

class UdsClientImproved {
public:
    explicit UdsClientImproved(std::string uds_name, ClientConfig config = {});
    ~UdsClientImproved();
    DISABLE_COPY_AND_MOVE(UdsClientImproved)

    // 主要接口
    [[nodiscard]] RequestStatus SyncRequest(
        type::Buffer const& request_buffer, 
        type::Buffer& response_buffer,
        std::optional<std::chrono::nanoseconds> timeout = std::nullopt);

    // 状态查询
    [[nodiscard]] ConnectionState GetConnectionState() const noexcept { 
        return connection_state_.load(); 
    }
    
    [[nodiscard]] bool IsConnected() const noexcept { 
        return connection_state_.load() == ConnectionState::kConnected; 
    }
    
    // 统计信息
    [[nodiscard]] ClientStatistics GetStatistics() const noexcept { return statistics_; }
    void ResetStatistics() noexcept;
    
    // 配置管理
    [[nodiscard]] ClientConfig const& GetConfig() const noexcept { return config_; }
    void UpdateConfig(ClientConfig const& new_config) noexcept { config_ = new_config; }

private:
    // 连接管理
    [[nodiscard]] std::error_code Connect() noexcept;
    void CloseConnect() noexcept;
    [[nodiscard]] std::error_code ReConnect() noexcept;

    // 网络操作
    [[nodiscard]] std::error_code SetSocketTimeout(std::chrono::nanoseconds timeout) noexcept;
    [[nodiscard]] std::error_code SendRequest(type::Buffer const& request_buffer) noexcept;
    [[nodiscard]] std::error_code ReceiveResponse(type::Buffer& response_buffer) noexcept;

    // 工具函数
    void UpdateStatistics(RequestStatus status, size_t bytes_sent = 0, size_t bytes_received = 0) noexcept;
    [[nodiscard]] bool IsValidSocketFd() const noexcept { return sock_fd_ >= 0; }
    [[nodiscard]] std::string GetSocketPath() const;

    // 成员变量
    std::string uds_name_;
    ClientConfig config_;
    int32_t sock_fd_{-1};
    std::atomic<ConnectionState> connection_state_{ConnectionState::kDisconnected};
    std::atomic<uint32_t> request_sequence_{0};
    
    // 统计信息
    mutable ClientStatistics statistics_;

    // 常量
    static constexpr char const* kLogTag{"uds-client-improved"};
};

// 错误代码定义
enum class UdsClientError {
    kSuccess = 0,
    kSocketCreationFailed,
    kConnectionFailed,
    kTimeoutError,
    kSendFailed,
    kReceiveFailed,
    kSerializationFailed,
    kMessageTooLarge,
    kInvalidState
};

// 错误代码转换
std::error_code make_error_code(UdsClientError e) noexcept;

}  // namespace ws::uds

// 注册错误代码
namespace std {
template<>
struct is_error_code_enum<ws::uds::UdsClientError> : true_type {};
}

#endif  // UDS_CLIENT_IMPROVED_HPP_
