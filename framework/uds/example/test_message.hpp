#ifndef TEST_MESSAGE_HPP_
#define TEST_MESSAGE_HPP_

#include <string>

#include "type/serializable.hpp"

namespace ws::uds {

struct TestRequest : public type::Serializable {
    int32_t id;
    // int32_t mode;
    SERIALIZABLE(id);
};

struct TestResponse : public type::Serializable {
    int32_t id;
    int32_t status;
    SERIALIZABLE(id, status);
};

}  // namespace ws::uds

#endif  // TEST_MESSAGE_HPP_