
add_executable(request_test request_test.cpp)
target_link_libraries(request_test PUBLIC uds fmt)
target_link_directories(request_test PUBLIC "/usr/local/lib/x86_64-linux-gnu/")


add_executable(response_test response_test.cpp)
target_link_libraries(response_test PUBLIC uds fmt)
target_link_directories(response_test PUBLIC "/usr/local/lib/x86_64-linux-gnu/")

# 超时一致性测试
add_executable(timeout_consistency_server timeout_consistency_server.cpp)
target_link_libraries(timeout_consistency_server PUBLIC uds fmt)
target_link_directories(timeout_consistency_server PUBLIC "/usr/local/lib/x86_64-linux-gnu/")

add_executable(timeout_consistency_client timeout_consistency_client.cpp)
target_link_libraries(timeout_consistency_client PUBLIC uds fmt)
target_link_directories(timeout_consistency_client PUBLIC "/usr/local/lib/x86_64-linux-gnu/")

# 简单的超时测试（单进程）
add_executable(simple_timeout_test simple_timeout_test.cpp)
target_link_libraries(simple_timeout_test PUBLIC uds fmt)
target_link_directories(simple_timeout_test PUBLIC "/usr/local/lib/x86_64-linux-gnu/")

# 超时一致性测试
add_executable(test_timeout_consistency test_timeout_consistency.cpp)
target_link_libraries(test_timeout_consistency PUBLIC uds fmt)
target_link_directories(test_timeout_consistency PUBLIC "/usr/local/lib/x86_64-linux-gnu/")