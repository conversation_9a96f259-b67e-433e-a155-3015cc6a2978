#include <chrono>
#include <thread>

#include "test_message.hpp"
#include "uds/uds_server.hpp"

using TestRequest = ws::uds::TestRequest;
using TestResponse = ws::uds::TestResponse;
static constexpr char const* kLogTag = "response_test";

int main() {
    ws::uds::UdsServer<TestRequest, TestResponse> server{"test_uds"};
    uid_t const alowed_uid = 0;
    server.AddUidAuthWhiteList(alowed_uid);

    std::this_thread::sleep_for(std::chrono::seconds(1));

    int status_responsed = 1;

    server.AsyncResponses([&status_responsed](TestRequest const& request, TestResponse& response) {
        response.id = status_responsed++;
        response.status = 0;

        ws::log::DebugFmt(kLogTag, "=======================");
        ws::log::DebugFmt(kLogTag, "模式服务端模拟执行5s，这时由于客户端第一次设置timeout为1s,已经超时");
        std::this_thread::sleep_for(std::chrono::seconds(5));

        ws::log::DebugFmt(kLogTag, "服务端完成工作后回复，request,id: {},回复的response.id: {}", request.id,
                          response.id);

        return ws::uds::kUserRequestSucc;
    });

    std::this_thread::sleep_for(std::chrono::seconds(1));
    while (true) {
    }

    return 0;
}