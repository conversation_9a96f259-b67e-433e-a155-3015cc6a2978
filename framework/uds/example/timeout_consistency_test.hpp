#ifndef TIMEOUT_CONSISTENCY_TEST_HPP_
#define TIMEOUT_CONSISTENCY_TEST_HPP_

#include <string>

#include "type/serializable.hpp"

namespace ws::uds {

struct TimeoutTestRequest : public type::Serializable {
    int32_t request_id;
    std::string request_data;
    int32_t processing_time_seconds;  // 服务端处理时间（秒）
    
    SERIALIZABLE(request_id, request_data, processing_time_seconds);
};

struct TimeoutTestResponse : public type::Serializable {
    int32_t request_id;  // 对应的请求ID
    std::string response_data;
    int64_t server_timestamp;  // 服务端处理时的时间戳
    
    SERIALIZABLE(request_id, response_data, server_timestamp);
};

}  // namespace ws::uds

#endif  // TIMEOUT_CONSISTENCY_TEST_HPP_
