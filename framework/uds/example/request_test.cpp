#include <unistd.h>

#include <chrono>
#include <iostream>
#include <thread>

#include "log/log.hpp"
#include "test_message.hpp"
#include "uds/uds_client.hpp"

using TestRequest = ws::uds::TestRequest;
using TestResponse = ws::uds::TestResponse;

static constexpr char const* kLogTag = "request_test";

int main() {
    ws::log::DebugFmt(kLogTag, "curr pid: {}, uid: {}, gid: {}", getpid(), getuid(), getgid());

    ws::uds::UdsClient<TestRequest, TestResponse> client{"test_uds"};
    TestRequest request{};
    TestResponse response{};

    request.id = 1;

    std::this_thread::sleep_for(std::chrono::seconds(1));

    auto expire_1s = std::chrono::nanoseconds(1000000000);  // 1s
    auto status = client.SyncRequest(request, response, expire_1s);
    ws::log::DebugFmt(kLogTag, "status: {}", status);

    auto expire_10s = std::chrono::nanoseconds(10000000000);  // 10s
    request.id = 2;
    status = client.SyncRequest(request, response, expire_10s);
    ws::log::DebugFmt(kLogTag, "status: {}", status);

    // AsyncRequest.

    while (true) {
    }
    return 0;
}
