#ifndef UDS_SERVER_HPP_
#define UDS_SERVER_HPP_

#include <functional>
#include <memory>

#include "log/log.hpp"
#include "type/serializer.hpp"
#include "uds/uds_define.hpp"
#include "utils/macros.hpp"

namespace ws::uds {

// Forward declaration
class UdsServerImpl;

class UdsServerTypeless {
    using UdsServerTypelessCallback = std::function<int32_t(type::Buffer const&, type::Buffer&)>;

public:
    explicit UdsServerTypeless(std::string const& uds_name, bool const enable_auth);
    ~UdsServerTypeless();
    DISABLE_COPY_AND_MOVE(UdsServerTypeless)
    void AsyncResponses(UdsServerTypelessCallback&& callback);
    bool StopListener();

    void AddUidAuthWhiteList(uid_t const uid);
    void AddGidAuthWhiteList(gid_t const gid);
    void AddAuthWhiteList(uid_t const uid, gid_t const gid);

private:
    std::unique_ptr<UdsServerImpl> pimpl_;
};

template <typename RequestType, typename ResponseType>
class UdsServer {
    using UdsResponseCallback = std::function<int32_t(RequestType const&, ResponseType&)>;

public:
    /**
     * \brief Construct a new UdsServer object
     *
     * \param [dir] uds_name    The path of the unix domain socket.
     * \param [dir] enable_auth Wheather to enable authentication.
     * Default is true, If set to false, the server will not check the authentication.
     */
    explicit UdsServer(std::string const& uds_name, bool const enable_auth = true)
        : uds_name_{uds_name}, typeless_{std::make_unique<UdsServerTypeless>(uds_name, enable_auth)} {}

    /**
     * \brief Destroy the UdsServer object.
     *
     */
    ~UdsServer() noexcept = default;
    DISABLE_COPY_AND_MOVE(UdsServer)

    /**
     * \brief  Start waiting for requests from clients.
     *
     * \param [dir] callback The callback function to handle the request.
     */
    void AsyncResponses(UdsResponseCallback&& callback) {
        auto request_callback = [this, callback = std::move(callback)](type::Buffer const& request_buffer,
                                                                       type::Buffer& response_buffer) {
            type::Serializer<RequestType> request_serializer;
            RequestType request{};
            // 对于Server端来说，将request_buffer反序列化到request.
            if (!request_serializer.FromBuffer(request_buffer, request)) {
                log::ErrorFmt(kLogTag, "client sync request serialize request fail. uds_name: {}", uds_name_);
                return kUserRequestFail;
            }

            ResponseType response{};
            // 这里的ServiceStatus特别注意
            // 这里应该是Server端收到的request后，写入response。
            int32_t const status = callback(request, response);

            type::Serializer<ResponseType> response_serializer;
            // 将response中的内容序列化到response_buffer返回给Client端.
            if (!response_serializer.ToBuffer(response, response_buffer)) {
                log::ErrorFmt(kLogTag, "client sync request serialize response fail. uds_name: {}", uds_name_);
                return kUserRequestFail;
            }

            return status;
        };
        return typeless_->AsyncResponses(std::move(request_callback));
    }

    /**
     * \brief  Server actively turns off receiving client requests.
     *
     * @return true     Turn off successfully.
     * @return false    Turn off failed.
     */
    bool StopListener() { return typeless_->StopListener(); }

    /**
     * \brief  Added a whitelist of uids that are allowed to communicate, which only takes effect when authentication is
     * enabled.
     *
     * \param [dir] uid Uid of the user.
     */
    void AddUidAuthWhiteList(uid_t const uid) { typeless_->AddUidAuthWhiteList(uid); }

    /**
     * \brief  Added a whitelist of gids that are allowed to communicate, which only takes effect when authentication is
     *
     * \param [dir] gid Gid of the group.
     */
    void AddGidAuthWhiteList(gid_t const gid) { typeless_->AddGidAuthWhiteList(gid); }

    /**
     * \brief  Added a whitelist of uids and gids that are allowed to communicate, which only takes effect when
     *
     * \param [dir] uid Uid of the user.
     * \param [dir] gid Gid of the group.
     */
    void AddAuthWhiteList(uid_t const uid, gid_t const gid) { typeless_->AddAuthWhiteList(uid, gid); }

private:
    std::string uds_name_{};
    std::unique_ptr<UdsServerTypeless> typeless_{nullptr};
    static constexpr char const* kLogTag{"uds-server"};
};

}  // namespace ws::uds

#endif  // UDS_SERVER_HPP_