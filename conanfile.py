
lios_components = []
components_dependencies_graph = {}

def str_trim(str, start, end):
    regex_str = start + "(.+?)" + end
    result = re.findall(regex_str, str)
    str_output = "".join(result)
    return str_output

def remove_everything_after_separator_in_string(origin_str, separator):
    result = origin_str.split(separator, 1)[0]
    return result

def cmd(command):
    os.environ["PYTHONUNBUFFERED"] = "1"
    
    p = subprocess.Popen(command,
                         stdout=subprocess.PIPE,
                         stderr=subprocess.STDOUT,
                         )

    output = []
    for line in p.stdout:
        line = line.decode()
        line = line.strip("\n")
        output.append(line.strip())
    return output

def package_info(self):
    self.conan_package_install_path = self.package_folder
    self.conan_package_install_lib_path = f"{self.package_folder}/lib"
    
    self.cpp_info.set_property("cmake_file_name", "LIOS")
    self.cpp_info.set_property("cmake_find_mode", "both")
    
    self.cpp_info.names["cmake_find_package"] = "LIOS"
    self.cpp_info.names["cmake_find_package_multi"] = "LIOS"
    
    self.get_lios_components()
    self.get_lios_components_dependencies()

    for component, dependencies in components_dependencies_graph.items():
        self.cpp_info.components[component].set_property("cmake_target_name", f"LIOS::{component}")
        self.cpp_info.components[component].set_property("pkg_config_name", component)
        self.cpp_info.components[component].names["cmake_find_package"] = component
        self.cpp_info.components[component].libs = [component]
        
        for dependency in dependencies:
            self.cpp_info.components[component].requires.append(dependency)


def get_lios_components(self):
    install_lib_list = os.listdir(f"{self.conan_package_install_lib_path}")
    for install_lib in install_lib_list:
        lios_component = remove_everything_after_separator_in_string(install_lib, ".so")[3:]
        if lios_component not in lios_components:
            lios_components.append(lios_component)
            

def get_lios_components_dependencies(self):
    for lios_component in lios_components:
        self.get_dependencied_for_each_component(lios_component)
        
def get_dependencied_for_each_component(self, curr_component):
    curr_component_file = f"{self.conan_package_install_lib_path}/lib{curr_component}.so"
    curr_component_dependencies_list = cmd(f"readelf -d {curr_component_file} | grep NEEDED")
    components_dependencies_graph[curr_component] = []
    
    for component_dependency in curr_component_dependencies_list:
        dependency_shared_library = str_trim(component_dependency, "\[", "]").split(".")[0][3:]
        if dependency_shared_library in lios_components:
            components_dependencies_graph[curr_component].append(dependency_shared_library)