macro(set_compiler_options)
    set(CMAKE_VERBOSE_MAKEFILE ON)

    set(CMAKE_C_STANDARD 11)

    set(CMAKE_CXX_STANDARD 20)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)

    if (CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
        add_compile_options(-Wall -Wextra -Werror -Wno-unused-parameter)
    endif()

    if (CMAKE_CXX_COMPILER_ID MATCHES "GNU")
        add_compile_options(-fdiagnostics-color=always)
    elseif (CMAKE_CXX_COMPILER_ID MATCHES "Clang")
        add_compile_options(-fcolor-diagnostics -Wno-c11-extensions -Wno-c++20-extensions -Wno-unused-private-field
            -Wno-deprecated-declarations)
    endif()

    add_compile_options(-fPIC)
endmacro()