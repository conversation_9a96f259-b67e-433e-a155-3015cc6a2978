

macro(target_include_module_directories)
    set(multi_value_args PUBLIC PRIVATE INTERFACE)
    cmake_parse_arguments(ARG "${options}" "${one_value_args}" "${multi_value_args}" ${ARGN})
    
    list(POP_FRONT ARG_UNPARSED_ARGUMENTS target_name)

    get_property(ROOT_DIR GLOBAL PROPERTY WORKSPACE_ROOT_DIR)
    set(src_dir ${ROOT_DIR}/framework)

    foreach(module ${ARG_PUBLIC})
        target_include_directories(${target_name} PUBLIC ${src_dir}/${module}/include)
    endforeach()

    foreach(module ${ARG_PRIVATE})
        target_include_directories(${target_name} PRIVATE ${src_dir}/${module}/include)
    endforeach()

    foreach(module ${ARG_INTERFACE})
        target_include_directories(${target_name} INTERFACE ${src_dir}/${module}/include)
    endforeach()
endmacro()
